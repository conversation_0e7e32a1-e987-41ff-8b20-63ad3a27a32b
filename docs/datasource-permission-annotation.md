# 数据源权限校验注解使用指南

## 概述

`@CheckDataSourcePermission` 是一个自定义注解，用于在方法级别进行数据源权限校验。该注解支持 Spring SpEL 表达式，可以灵活地从方法参数、返回值或Spring容器中的Bean获取数据源标识，并在方法执行前进行权限校验。

## 功能特性

- 🔒 **自动权限校验**: 在方法执行前自动进行数据源访问权限验证
- 🎯 **SpEL表达式支持**: 灵活的表达式语言支持，可动态获取数据源标识
- 📝 **自定义错误消息**: 支持自定义权限校验失败时的错误消息
- 🏷️ **多种标识方式**: 支持使用数据源ID或数据源名称进行权限校验
- 🔧 **Bean方法调用**: 支持调用Spring容器中其他Bean的方法

## 注解参数

### @CheckDataSourcePermission

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| value | String | 是 | - | 数据源标识的SpEL表达式 |
| message | String | 否 | "没有数据源访问权限" | 权限校验失败时的错误消息 |
| isDatasourceName | boolean | 否 | false | 是否使用数据源名称进行校验(false表示使用ID) |

## 使用示例

### 1. 基本用法 - 从方法参数获取

```java
@Service
public class QueryService {
    
    /**
     * 从方法参数直接获取数据源ID
     */
    @CheckDataSourcePermission("#datasourceId")
    public String queryData(String datasourceId, String sql) {
        // 执行数据查询逻辑
        return "查询结果";
    }
}
```

### 2. 从参数对象属性获取

```java
@Service
public class DataAnalysisService {
    
    /**
     * 从参数对象的属性获取数据源ID
     */
    @CheckDataSourcePermission("#params.datasourceId")
    public AnalysisResult analyze(AnalysisParams params) {
        // 执行数据分析逻辑
        return new AnalysisResult();
    }
}
```

### 3. 使用数据源名称进行校验

```java
@Service
public class ReportService {
    
    /**
     * 使用数据源名称进行权限校验
     */
    @CheckDataSourcePermission(
        value = "#datasourceName", 
        isDatasourceName = true, 
        message = "您没有访问该数据源的权限"
    )
    public ReportData generateReport(String datasourceName, String reportType) {
        // 生成报表逻辑
        return new ReportData();
    }
}
```

### 4. 调用其他Bean的方法

```java
@Service
public class QueryExecutor {
    
    /**
     * 调用其他Bean的方法获取数据源ID
     */
    @CheckDataSourcePermission("@queryConfigService.getDatasourceIdByQueryId(#queryId)")
    public QueryResult executeStoredQuery(String queryId) {
        // 执行存储的查询
        return new QueryResult();
    }
}
```

### 5. 复杂的SpEL表达式

```java
@Service
public class SmartQueryService {
    
    /**
     * 使用条件表达式选择数据源ID
     */
    @CheckDataSourcePermission(
        "#request.datasourceId != null ? #request.datasourceId : @configService.getDefaultDatasourceId()"
    )
    public QueryResult smartQuery(QueryRequest request) {
        // 智能查询逻辑
        return new QueryResult();
    }
    
    /**
     * 根据用户类型选择不同的数据源
     */
    @CheckDataSourcePermission(
        "#userType == 'admin' ? @configService.getAdminDatasourceId() : @configService.getUserDatasourceId()"
    )
    public DataSet getUserData(String userType, String userId) {
        // 获取用户数据
        return new DataSet();
    }
}
```

## SpEL表达式语法参考

### 基本语法

| 表达式 | 说明 | 示例 |
|--------|------|------|
| `#paramName` | 获取方法参数 | `#datasourceId` |
| `#param.property` | 获取参数对象属性 | `#request.datasourceId` |
| `@beanName` | 引用Spring Bean | `@datasourceService` |
| `@beanName.method()` | 调用Bean方法 | `@configService.getDefaultId()` |
| `condition ? value1 : value2` | 三元条件操作符 | `#id != null ? #id : 'default'` |

### 常用表达式示例

```java
// 1. 直接获取参数
@CheckDataSourcePermission("#datasourceId")

// 2. 获取对象属性
@CheckDataSourcePermission("#request.datasourceId")

// 3. 调用Bean方法
@CheckDataSourcePermission("@queryService.getDatasourceId(#queryId)")

// 4. 条件判断
@CheckDataSourcePermission("#dsId != null ? #dsId : @configService.getDefaultDatasourceId()")

// 5. 字符串拼接
@CheckDataSourcePermission("'datasource_' + #type + '_' + #env")

// 6. 复杂对象导航
@CheckDataSourcePermission("#request.metadata.datasource.id")
```

## 权限校验流程

1. **用户身份验证**: 检查当前用户是否已登录
2. **表达式解析**: 使用SpEL解析器解析表达式获取数据源标识
3. **数据源查找**: 根据标识(ID或名称)查找数据源信息
4. **权限校验**: 调用权限服务检查用户对该数据源的访问权限
5. **异常处理**: 权限校验失败时抛出相应的业务异常

## 异常处理

权限校验失败时会抛出不同类型的 `BusinessException`：

| HTTP状态码 | 错误码 | 场景 |
|------------|--------|------|
| 401 | 401 | 用户未登录 |
| 400 | 400 | 数据源标识为空或SpEL表达式解析失败 |
| 404 | 404 | 数据源不存在 |
| 403 | 403 | 没有数据源访问权限 |
| 500 | 500 | 系统内部错误 |

## 注意事项

1. **性能考虑**: 权限校验会在每次方法调用时执行，建议合理使用缓存机制
2. **表达式安全**: SpEL表达式功能强大，但要注意表达式的安全性，避免执行危险操作
3. **Bean依赖**: 使用 `@beanName` 方式时，确保目标Bean在Spring容器中存在
4. **参数命名**: 使用 `#paramName` 时，确保参数名称正确(建议使用 `-parameters` 编译选项)
5. **异常处理**: 在使用该注解的方法中，要有适当的异常处理机制

## 最佳实践

1. **统一命名**: 建议在项目中统一数据源标识的参数命名规范
2. **错误消息**: 为不同的业务场景提供清晰的错误消息
3. **日志记录**: 关键的权限校验操作会自动记录日志，便于审计和调试
4. **测试覆盖**: 为使用该注解的方法编写完整的单元测试和集成测试
5. **文档维护**: 在团队中维护SpEL表达式的使用文档和最佳实践

## 与现有权限系统集成

该注解与项目现有的权限系统(基于Yuia的权限框架)无缝集成：

- 自动获取当前登录用户信息
- 调用现有的 `AuthService.checkAuth()` 方法进行权限校验
- 支持现有的数据源管理和权限配置

通过这种设计，可以在不改变现有权限架构的基础上，为数据源访问提供细粒度的方法级权限控制。 