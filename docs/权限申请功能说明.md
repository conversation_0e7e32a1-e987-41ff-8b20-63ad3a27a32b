# 权限申请功能说明

## 功能概述

在数据源详情页面添加了"申请授权"按钮，用户可以查看自己的权限状态并申请相应的权限。支持数据源、表、列三个层级的权限申请。

## 功能特性

### 前端功能

1. **申请授权按钮**
   - 在数据源详情页面的数据源、表、列级别都添加了"申请授权"按钮
   - 按钮采用蓝色主题，与"授权配置"按钮区分
   - **重要：只有启用了授权配置（isAuthRequired=true）的资源才会显示"申请授权"按钮**

2. **权限申请弹窗 (AuthRequestModal.vue)**
   - 显示申请资源的基本信息
   - 查询并显示用户当前的权限状态
   - 提供申请理由输入框
   - 支持提交权限申请

3. **权限状态显示**
   - 已有权限：绿色显示
   - 申请审核中：黄色显示
   - 申请被拒绝：红色显示
   - 申请已过期：灰色显示
   - 无权限：灰色显示

### 后端功能

1. **权限申请实体 (AuthRequest)**
   - 支持数据源、Schema、表、列四种资源类型
   - 记录申请理由、申请时间、审批状态等信息
   - 支持权限过期时间管理

2. **权限申请API**
   - `GET /api/auth/permissions/status` - 查询用户权限状态
   - `POST /api/auth/permissions/request` - 提交权限申请
   - `GET /api/auth/permissions/requests` - 查询用户申请列表
   - `GET /api/auth/permissions/admin/requests` - 管理员查询所有申请
   - `POST /api/auth/permissions/admin/approve/{requestId}` - 审批权限申请
   - `GET /api/auth/permissions/check` - 检查用户权限
   - `DELETE /api/auth/permissions/revoke/{requestId}` - 撤销权限申请

3. **数据库表**
   - `auth_request` 表存储权限申请记录
   - 支持索引优化查询性能

## 使用方法

### 用户申请权限

1. 进入数据源详情页面
2. **确认资源已启用授权配置**：只有显示"授权配置(已启用)"的资源才会有"申请授权"按钮
3. 在需要申请权限的资源（数据源/表/列）旁点击"申请授权"按钮
4. 在弹出的申请弹窗中查看当前权限状态
5. 填写申请理由
6. 点击"提交申请"

### 管理员配置授权

1. 在数据源详情页面点击"授权配置"按钮
2. 勾选"需要授权"选项
3. 保存配置
4. 此时该资源会显示"申请授权"按钮供用户申请权限

### 管理员审批权限

1. 通过管理员API查询待审批的权限申请
2. 调用审批API批准或拒绝申请
3. 被批准的权限默认有效期为1年

## 技术实现

### 前端技术栈

- Vue 3 + TypeScript
- Tailwind CSS 样式
- 组件化设计

### 后端技术栈

- Spring Boot
- MyBatis Plus
- MySQL 数据库
- RESTful API

### 关键文件

**前端文件：**
- `boss-data-scope2/src/components/datasource/AuthRequestModal.vue` - 权限申请弹窗组件
- `boss-data-scope2/src/components/datasource/DataSourceDetail.vue` - 数据源详情页面（已添加申请按钮）
- `boss-data-scope2/src/api/auth.ts` - 权限相关API接口
- `boss-data-scope2/src/types/auth.ts` - 权限相关类型定义

**后端文件：**
- `src/main/java/com/example/datascope/entity/AuthRequest.java` - 权限申请实体
- `src/main/java/com/example/datascope/dto/AuthRequestDTO.java` - 权限申请DTO
- `src/main/java/com/example/datascope/dto/UserPermissionStatusDTO.java` - 用户权限状态DTO
- `src/main/java/com/example/datascope/service/AuthRequestService.java` - 权限申请服务接口
- `src/main/java/com/example/datascope/service/impl/AuthRequestServiceImpl.java` - 权限申请服务实现
- `src/main/java/com/example/datascope/controller/AuthRequestController.java` - 权限申请控制器
- `src/main/java/com/example/datascope/mapper/AuthRequestMapper.java` - 权限申请Mapper
- `src/main/resources/mapper/AuthRequestMapper.xml` - MyBatis映射文件
- `src/main/resources/db/migration/V1.2__Create_auth_request_table.sql` - 数据库迁移脚本

## 测试

### 前端测试

访问 `http://localhost:3000/#/test-auth-request` 可以测试权限申请功能。

### API测试

可以通过 Swagger UI 访问 `http://localhost:8080/data-scope/v3/api-docs` 查看和测试API接口。

## 注意事项

1. 当前用户身份识别采用简化实现，实际部署时需要集成真实的认证系统
2. 权限申请的审批流程可以根据实际需求进行扩展
3. 权限过期时间可以根据业务需求进行调整
4. 建议在生产环境中添加更多的权限验证和安全检查

## 后续扩展

1. 添加权限申请的邮件通知功能
2. 实现权限申请的审批工作流
3. 添加权限使用情况的统计和监控
4. 支持批量权限申请和审批
5. 添加权限申请的历史记录查询
