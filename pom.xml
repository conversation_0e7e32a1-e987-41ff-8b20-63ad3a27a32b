<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.datascope</groupId>
    <artifactId>data-scope</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>DataScope</name>
    <description>Intelligent Data Discovery and Query System</description>

    <properties>
        <java.version>8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <!-- Dependency Versions -->
        <!-- Framework & Core Dependencies -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <mybatis.version>3.5.6</mybatis.version>
        <mybatis-spring.version>2.0.4</mybatis-spring.version>
        <mybatis-plus.version>3.4.3</mybatis-plus.version>
        <dynamic-datasource.version>3.5.0</dynamic-datasource.version>
        <mysql.version>8.0.33</mysql.version>
        <db2-jdbc.version>********</db2-jdbc.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mapstruct-lombok.version>0.2.0</mapstruct-lombok.version>
        <commons-lang3.version>3.13.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-text.version>1.11.0</commons-text.version>
        <commons-csv.version>1.10.0</commons-csv.version>
        <guava.version>32.1.2-jre</guava.version>
        <jackson.version>2.15.3</jackson.version>
        <springdoc.version>2.3.0</springdoc.version>
        <junit-jupiter.version>5.10.0</junit-jupiter.version>
        <mockito.version>4.11.0</mockito.version>
        <assertj.version>3.24.2</assertj.version>
        <testcontainers.version>1.19.1</testcontainers.version>
        <flyway.version>10.7.1</flyway.version>
        <hikaricp.version>5.1.0</hikaricp.version>
        <h2.version>2.2.220</h2.version>
        <poi.version>5.2.5</poi.version>

        <!-- Validation & Security -->
        <jakarta.validation.version>3.0.2</jakarta.validation.version>
        <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
        <springdoc.version>2.3.0</springdoc.version>

        <!-- Testing -->
        <testcontainers.version>1.19.3</testcontainers.version>

        <!-- Utils -->
        <commons-lang3.version>3.13.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <guava.version>32.1.2-jre</guava.version>
        <jackson.version>2.15.3</jackson.version>
        <jmh.version>1.33</jmh.version>
        <archunit.version>1.2.1</archunit.version>
        <yeepay-utils-common.version>4.2.0</yeepay-utils-common.version>
    </properties>

    <modules>
        <!--        <module>data-scope-domain</module>-->
        <!--        <module>data-scope-infrastructure</module>-->
        <module>data-scope-app</module>
        <module>data-scope-main</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Internal Modules -->
            <dependency>
                <groupId>com.datascope</groupId>
                <artifactId>data-scope-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.datascope</groupId>-->
            <!--                <artifactId>data-scope-domain</artifactId>-->
            <!--                <version>${project.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>com.datascope</groupId>-->
            <!--                <artifactId>data-scope-infrastructure</artifactId>-->
            <!--                <version>${project.version}</version>-->
            <!--            </dependency>-->

            <!-- MyBatis -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- MySQL -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- DB2 -->
            <dependency>
                <groupId>com.ibm.db2</groupId>
                <artifactId>jcc</artifactId>
                <version>${db2-jdbc.version}</version>
            </dependency>

            <!-- HikariCP -->
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>

            <!-- Database Migration -->
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-mysql</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${mapstruct-lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- JWT Library - 升级版本以兼容Jackson 2.15.x -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.5.0</version>
            </dependency>

            <!-- SpringDoc OpenAPI -->
            <!--            <dependency>-->
            <!--                <groupId>org.springdoc</groupId>-->
            <!--                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>-->
            <!--                <version>${springdoc.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>1.7.0</version>
            </dependency>

            <!-- 国密组件 -->
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yp-gm-crypt</artifactId>
                <version>1.0.7.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 运营后台权限框架 -->
            <dependency>
                <groupId>com.yeepay.g3</groupId>
                <artifactId>uia-sdk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>object-storage</groupId>
                        <artifactId>object-storage-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-utils-common</artifactId>
                <version>${yeepay-utils-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-thread-context</artifactId>
                <version>1.8</version>
            </dependency>

            <!-- Excel处理 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <failOnError>true</failOnError>
                        <verbose>true</verbose>
                        <fork>true</fork>
                        <compilerArgument>-nowarn</compilerArgument>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${mapstruct-lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.openjdk.jmh</groupId>
                                <artifactId>jmh-generator-annprocess</artifactId>
                                <version>${jmh.version}</version>
                            </path>
                            <path>
                                <groupId>com.tngtech.archunit</groupId>
                                <artifactId>archunit-junit4</artifactId>
                                <version>0.23.1</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>false</skip>
                    </configuration>
                </plugin>
                <!--<plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <argLine>
                            &#45;&#45;add-opens=java.base/java.lang=ALL-UNNAMED
                            &#45;&#45;add-opens=java.base/java.util=ALL-UNNAMED
                        </argLine>
                    </configuration>
                </plugin>-->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.7.18</version>
                </plugin>




                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.5</version>
                    <configuration>
<!--                        <argLine>-->
<!--                            -Dnet.bytebuddy.experimental=true-->
<!--                            &#45;&#45;add-opens java.base/java.lang.invoke=ALL-UNNAMED-->
<!--                            &#45;&#45;add-opens java.base/java.lang.reflect=ALL-UNNAMED-->
<!--                            &#45;&#45;add-opens java.base/java.io=ALL-UNNAMED-->
<!--                            &#45;&#45;add-opens java.base/java.util=ALL-UNNAMED-->
<!--                        </argLine>-->
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.11</version>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>
</project>
