spring:
  # 数据库配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=MySQL
    username: sa
    password:

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # 禁用Spring Security
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - com.yeepay.g3.core.yuia.yuiacommons.patronclient.DefaultPatronConfiguration
  config:
    activate:
      on-profile: test

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.datascope.app.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    root: info
    com.datascope: debug
    org.springframework.jdbc: debug

# 安全配置
datascope:
  security:
    # 测试环境使用固定密钥
    aes-key: test-aes-key-for-testing-only
    salt: test-salt-for-testing-only
    sm4-key: 1234567890abcdef

# 测试环境配置
test:
  mock:
    auth:
      enabled: true
      username: test_user
