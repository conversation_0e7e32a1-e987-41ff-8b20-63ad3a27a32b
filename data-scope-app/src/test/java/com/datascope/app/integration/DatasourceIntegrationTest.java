package com.datascope.app.integration;

import com.datascope.app.dto.datasource.CreateDataSourceRequest;
import com.datascope.app.dto.datasource.DataSourceDTO;
import com.datascope.app.dto.datasource.TestConnectionRequest;
import com.datascope.app.dto.datasource.TestConnectionResultDTO;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.util.DatabaseConnectionTester;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 数据源集成测试类
 * 使用模拟的服务和数据库连接测试器
 */
public class DatasourceIntegrationTest {

    @Mock
    private DatasourceService datasourceService;

    @Mock
    private DatabaseConnectionTester databaseConnectionTester;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试MySQL数据源连接")
    public void testMySQLConnection() {
        // 创建测试请求
        TestConnectionRequest request = new TestConnectionRequest();
        request.setType("mysql");
        request.setHost("localhost");
        request.setPort(3306);
        request.setDatabaseName("test_db");
        request.setUsername("test_user");
        request.setPassword("test_password");

        // 模拟服务返回结果
        TestConnectionResultDTO mockResult = new TestConnectionResultDTO();
        mockResult.setSuccess(false);
        mockResult.setMessage("数据库连接失败: Access denied for user 'test_user'@'localhost' (using password: YES)");
        when(datasourceService.testNewConnection(any(TestConnectionRequest.class))).thenReturn(mockResult);

        // 执行连接测试
        TestConnectionResultDTO result = datasourceService.testNewConnection(request);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("数据库连接失败") ||
                  result.getMessage().contains("Access denied"));
    }

    @Test
    @DisplayName("测试DB2数据源连接")
    public void testDB2Connection() {
        // 创建测试请求
        TestConnectionRequest request = new TestConnectionRequest();
        request.setType("db2");
        request.setHost("localhost");
        request.setPort(50000);
        request.setDatabaseName("test_db");
        request.setUsername("db2inst1");
        request.setPassword("test");

        // 模拟服务返回结果
        TestConnectionResultDTO mockResult = new TestConnectionResultDTO();
        mockResult.setSuccess(false);
        mockResult.setMessage("数据库连接失败: [jcc][t4][2043][11550][4.32.28] Exception java.net.ConnectException: Error opening socket to server localhost/127.0.0.1 on port 50,000 with message: Connection refused. ERRORCODE=-4499, SQLSTATE=08001");
        when(datasourceService.testNewConnection(any(TestConnectionRequest.class))).thenReturn(mockResult);

        // 执行连接测试
        TestConnectionResultDTO result = datasourceService.testNewConnection(request);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("数据库连接失败") ||
                  result.getMessage().contains("Connection refused"));
    }

    @Test
    @DisplayName("测试创建MySQL数据源")
    public void testCreateMySQLDatasource() {
        // 创建测试请求
        CreateDataSourceRequest request = new CreateDataSourceRequest();
        request.setName("测试MySQL数据源");
        request.setDescription("用于测试的MySQL数据源");
        request.setType("mysql");
        request.setHost("localhost");
        request.setPort(3306);
        request.setDatabaseName("test_db");
        request.setUsername("test_user");
        request.setPassword("test_password");
        request.setSyncFrequency("daily");

        // 模拟服务返回结果
        DataSourceDTO mockResult = new DataSourceDTO();
        mockResult.setId("test-mysql-id");
        mockResult.setName("测试MySQL数据源");
        mockResult.setType("mysql");
        mockResult.setHost("localhost");
        mockResult.setPort(3306);
        mockResult.setDatabase("test_db");
        mockResult.setUsername("test_user");
        mockResult.setIsActive(false);
        when(datasourceService.createDatasource(any(CreateDataSourceRequest.class))).thenReturn(mockResult);

        // 执行创建操作
        DataSourceDTO result = datasourceService.createDatasource(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试MySQL数据源", result.getName());
        assertEquals("mysql", result.getType());
        assertEquals("localhost", result.getHost());
        assertEquals(3306, result.getPort());
        assertEquals("test_db", result.getDatabase());
        assertEquals("test_user", result.getUsername());
        // 新创建的数据源应该是非活动状态
        assertFalse(result.getIsActive());
    }

    @Test
    @DisplayName("测试创建DB2数据源")
    public void testCreateDB2Datasource() {
        // 创建测试请求
        CreateDataSourceRequest request = new CreateDataSourceRequest();
        request.setName("测试DB2数据源");
        request.setDescription("用于测试的DB2数据源");
        request.setType("db2");
        request.setHost("localhost");
        request.setPort(50000);
        request.setDatabaseName("test_db");
        request.setUsername("db2inst1");
        request.setPassword("test");
        request.setSyncFrequency("daily");

        // 模拟服务返回结果
        DataSourceDTO mockResult = new DataSourceDTO();
        mockResult.setId("test-db2-id");
        mockResult.setName("测试DB2数据源");
        mockResult.setType("db2");
        mockResult.setHost("localhost");
        mockResult.setPort(50000);
        mockResult.setDatabase("test_db");
        mockResult.setUsername("db2inst1");
        mockResult.setIsActive(false);
        when(datasourceService.createDatasource(any(CreateDataSourceRequest.class))).thenReturn(mockResult);

        // 执行创建操作
        DataSourceDTO result = datasourceService.createDatasource(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试DB2数据源", result.getName());
        assertEquals("db2", result.getType());
        assertEquals("localhost", result.getHost());
        assertEquals(50000, result.getPort());
        assertEquals("test_db", result.getDatabase());
        assertEquals("db2inst1", result.getUsername());
        // 新创建的数据源应该是非活动状态
        assertFalse(result.getIsActive());
    }
}
