package com.datascope.app.service;

import com.datascope.app.dto.datasource.DataSourceDTO;
import com.datascope.app.dto.datasource.UpdateDataSourceRequest;
import com.datascope.app.entity.Datasource;
import com.datascope.app.service.impl.DatasourceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 数据源类型更新功能测试
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class DatasourceTypeUpdateTest {

    @InjectMocks
    private DatasourceServiceImpl datasourceService;

    private Datasource mockDatasource;
    private UpdateDataSourceRequest updateRequest;

    @BeforeEach
    void setUp() {
        // 创建模拟数据源
        mockDatasource = new Datasource();
        mockDatasource.setId("test-datasource-id");
        mockDatasource.setName("测试数据源");
        mockDatasource.setDescription("测试描述");
        mockDatasource.setType("mysql");
        mockDatasource.setHost("localhost");
        mockDatasource.setPort(3306);
        mockDatasource.setDatabaseName("test_db");
        mockDatasource.setUsername("test_user");
        mockDatasource.setStatus("active");
        mockDatasource.setSyncFrequency("manual");
        mockDatasource.setCreatedAt(new Date());
        mockDatasource.setUpdatedAt(new Date());
        mockDatasource.setNonce(1);

        // 创建更新请求
        updateRequest = new UpdateDataSourceRequest();
        updateRequest.setId("test-datasource-id");
        updateRequest.setName("更新后的数据源");
        updateRequest.setDescription("更新后的描述");
        updateRequest.setType("db2");  // 从mysql改为db2
        updateRequest.setHost("*************");
        updateRequest.setPort(50000);
        updateRequest.setDatabaseName("updated_db");
        updateRequest.setUsername("updated_user");
        updateRequest.setSyncFrequency("daily");
    }

    @Test
    void testUpdateDataSourceType_Success() {
        // 模拟获取现有数据源
        when(datasourceService.getById("test-datasource-id")).thenReturn(mockDatasource);

        // 模拟更新操作
        Datasource updatedDatasource = new Datasource();
        updatedDatasource.setId("test-datasource-id");
        updatedDatasource.setName("更新后的数据源");
        updatedDatasource.setDescription("更新后的描述");
        updatedDatasource.setType("db2");  // 验证类型已更新
        updatedDatasource.setHost("*************");
        updatedDatasource.setPort(50000);
        updatedDatasource.setDatabaseName("updated_db");
        updatedDatasource.setUsername("updated_user");
        updatedDatasource.setSyncFrequency("daily");
        updatedDatasource.setStatus("active");
        updatedDatasource.setCreatedAt(mockDatasource.getCreatedAt());
        updatedDatasource.setUpdatedAt(new Date());
        updatedDatasource.setNonce(2);

        when(datasourceService.getById("test-datasource-id")).thenReturn(updatedDatasource);

        // 执行更新
        DataSourceDTO result = datasourceService.updateDatasource(updateRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-datasource-id", result.getId());
        assertEquals("更新后的数据源", result.getName());
        assertEquals("更新后的描述", result.getDescription());
        assertEquals("db2", result.getType());  // 验证类型已正确更新
        assertEquals("*************", result.getHost());
        assertEquals(50000, result.getPort());
        assertEquals("updated_db", result.getDatabaseName());
        assertEquals("updated_user", result.getUsername());
        assertEquals("daily", result.getSyncFrequency());

        // 验证更新方法被调用
        verify(datasourceService, times(1)).getById("test-datasource-id");
    }

    @Test
    void testUpdateDataSourceType_OnlyTypeField() {
        // 测试只更新类型字段的情况
        UpdateDataSourceRequest typeOnlyRequest = new UpdateDataSourceRequest();
        typeOnlyRequest.setId("test-datasource-id");
        typeOnlyRequest.setType("postgresql");  // 只更新类型

        // 模拟获取现有数据源
        when(datasourceService.getById("test-datasource-id")).thenReturn(mockDatasource);

        // 模拟更新后的数据源
        Datasource updatedDatasource = new Datasource();
        updatedDatasource.setId("test-datasource-id");
        updatedDatasource.setName(mockDatasource.getName());
        updatedDatasource.setDescription(mockDatasource.getDescription());
        updatedDatasource.setType("postgresql");  // 只有类型被更新
        updatedDatasource.setHost(mockDatasource.getHost());
        updatedDatasource.setPort(mockDatasource.getPort());
        updatedDatasource.setDatabaseName(mockDatasource.getDatabaseName());
        updatedDatasource.setUsername(mockDatasource.getUsername());
        updatedDatasource.setSyncFrequency(mockDatasource.getSyncFrequency());
        updatedDatasource.setStatus(mockDatasource.getStatus());
        updatedDatasource.setCreatedAt(mockDatasource.getCreatedAt());
        updatedDatasource.setUpdatedAt(new Date());
        updatedDatasource.setNonce(2);

        when(datasourceService.getById("test-datasource-id")).thenReturn(updatedDatasource);

        // 执行更新
        DataSourceDTO result = datasourceService.updateDatasource(typeOnlyRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-datasource-id", result.getId());
        assertEquals("postgresql", result.getType());  // 验证类型已更新
        assertEquals(mockDatasource.getName(), result.getName());  // 其他字段保持不变
        assertEquals(mockDatasource.getDescription(), result.getDescription());
        assertEquals(mockDatasource.getHost(), result.getHost());
    }

    @Test
    void testUpdateDataSourceType_NullType() {
        // 测试type为null的情况
        UpdateDataSourceRequest nullTypeRequest = new UpdateDataSourceRequest();
        nullTypeRequest.setId("test-datasource-id");
        nullTypeRequest.setName("更新名称");
        nullTypeRequest.setType(null);  // type为null

        // 模拟获取现有数据源
        when(datasourceService.getById("test-datasource-id")).thenReturn(mockDatasource);

        // 模拟更新后的数据源（type应该保持原值）
        Datasource updatedDatasource = new Datasource();
        updatedDatasource.setId("test-datasource-id");
        updatedDatasource.setName("更新名称");
        updatedDatasource.setType("mysql");  // 保持原来的类型
        updatedDatasource.setDescription(mockDatasource.getDescription());
        updatedDatasource.setHost(mockDatasource.getHost());
        updatedDatasource.setPort(mockDatasource.getPort());
        updatedDatasource.setDatabaseName(mockDatasource.getDatabaseName());
        updatedDatasource.setUsername(mockDatasource.getUsername());
        updatedDatasource.setSyncFrequency(mockDatasource.getSyncFrequency());
        updatedDatasource.setStatus(mockDatasource.getStatus());
        updatedDatasource.setCreatedAt(mockDatasource.getCreatedAt());
        updatedDatasource.setUpdatedAt(new Date());
        updatedDatasource.setNonce(2);

        when(datasourceService.getById("test-datasource-id")).thenReturn(updatedDatasource);

        // 执行更新
        DataSourceDTO result = datasourceService.updateDatasource(nullTypeRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-datasource-id", result.getId());
        assertEquals("更新名称", result.getName());
        assertEquals("mysql", result.getType());  // 验证类型保持原值
    }

    @Test
    void testUpdateDataSourceType_EmptyType() {
        // 测试type为空字符串的情况
        UpdateDataSourceRequest emptyTypeRequest = new UpdateDataSourceRequest();
        emptyTypeRequest.setId("test-datasource-id");
        emptyTypeRequest.setName("更新名称");
        emptyTypeRequest.setType("");  // type为空字符串

        // 模拟获取现有数据源
        when(datasourceService.getById("test-datasource-id")).thenReturn(mockDatasource);

        // 模拟更新后的数据源（type应该保持原值）
        Datasource updatedDatasource = new Datasource();
        updatedDatasource.setId("test-datasource-id");
        updatedDatasource.setName("更新名称");
        updatedDatasource.setType("mysql");  // 保持原来的类型
        updatedDatasource.setDescription(mockDatasource.getDescription());
        updatedDatasource.setHost(mockDatasource.getHost());
        updatedDatasource.setPort(mockDatasource.getPort());
        updatedDatasource.setDatabaseName(mockDatasource.getDatabaseName());
        updatedDatasource.setUsername(mockDatasource.getUsername());
        updatedDatasource.setSyncFrequency(mockDatasource.getSyncFrequency());
        updatedDatasource.setStatus(mockDatasource.getStatus());
        updatedDatasource.setCreatedAt(mockDatasource.getCreatedAt());
        updatedDatasource.setUpdatedAt(new Date());
        updatedDatasource.setNonce(2);

        when(datasourceService.getById("test-datasource-id")).thenReturn(updatedDatasource);

        // 执行更新
        DataSourceDTO result = datasourceService.updateDatasource(emptyTypeRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-datasource-id", result.getId());
        assertEquals("更新名称", result.getName());
        assertEquals("mysql", result.getType());  // 验证类型保持原值
    }
}
