package com.datascope.app.util;

/**
 * SqlUtil手动测试类
 * <AUTHOR>
 */
public class SqlUtilManualTest {

    public static void main(String[] args) {
        System.out.println("=== SqlUtil.isValidSelectSql 功能测试 ===\n");

        // 测试有效的SELECT语句
        testValidSelectStatements();

        // 测试无效的SQL语句
        testInvalidSqlStatements();

        // 测试多语句SQL
        testMultipleStatements();

        // 测试字符串字面量处理
        testStringLiterals();

        // 测试括号嵌套
        testParenthesesNesting();

        // 测试大小写不敏感
        testCaseInsensitive();

        // 测试空白字符处理
        testWhitespaceHandling();

        // 性能测试
        testPerformance();

        // 边界条件测试
        testEdgeCases();

        System.out.println("\n=== 测试完成 ===");
    }

    private static void testValidSelectStatements() {
        System.out.println("1. 测试有效的SELECT语句:");

        String[] validSqls = {
            "SELECT * FROM users",
            "select id, name from users",
            "SELECT id, name FROM users WHERE id = 1",
            "SELECT * FROM users WHERE age > 18",
            "SELECT u.id, u.name, o.order_id FROM users u JOIN orders o ON u.id = o.user_id",
            "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders)",
            "SELECT id, name, (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count FROM users",
            "SELECT department, COUNT(*) as count FROM employees GROUP BY department",
            "SELECT * FROM users ORDER BY name ASC",
            "SELECT * FROM users LIMIT 10"
        };

        for (String sql : validSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testInvalidSqlStatements() {
        System.out.println("2. 测试无效的SQL语句:");

        String[] invalidSqls = {
            "INSERT INTO users (id, name) VALUES (1, 'test')",
            "UPDATE users SET name = 'test' WHERE id = 1",
            "DELETE FROM users WHERE id = 1",
            "CREATE TABLE users (id INT, name VARCHAR(50))",
            "DROP TABLE users",
            "ALTER TABLE users ADD COLUMN age INT",
            "TRUNCATE TABLE users",
            "",
            "   ",
            null
        };

        for (String sql : invalidSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql == null ? "null" : sql, result ? "失败" : "通过");
        }
        System.out.println();
    }

    private static void testMultipleStatements() {
        System.out.println("3. 测试多语句SQL:");

        String[] multiSqls = {
            "SELECT * FROM users \n SELECT * FROM orders",
            "SELECT id FROM users; SELECT name FROM users",
            "SELECT * FROM users",
            "select * from test \n" +
            "<where>\n" +
            "<if test=\"TYPENAME != null and TYPENAME != ''\">\n" +
            " TYPENAME = #{TYPENAME} \n" +
            " </if>\n" +
            "<if test=\"PAYMENT_TYPE2 != null and PAYMENT_TYPE2 != ''\">\n" +
            " and PAYMENT_TYPE2 = #{PAYMENT_TYPE2}\n" +
            " </if>  \n" +
            " and CRETM = #{CRETM}\n" +
            " and name like concat('%', '我', '%')\n" +
            " </where>\n" +
            "  order by sysconid desc",
            "SELECT * FROM users;"
        };

        for (String sql : multiSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testStringLiterals() {
        System.out.println("4. 测试字符串字面量处理:");

        String[] stringSqls = {
            "SELECT * FROM users WHERE name = 'John'",
            "SELECT * FROM users WHERE description = \"This is a test\"",
            "SELECT * FROM users WHERE description = 'This is an INSERT test'",
            "SELECT * FROM users WHERE comment = 'UPDATE this record'",
            "SELECT * FROM users WHERE name = 'O\\'Connor'"
        };

        for (String sql : stringSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testParenthesesNesting() {
        System.out.println("5. 测试括号嵌套:");

        String[] nestedSqls = {
            "SELECT * FROM users WHERE id IN (1, 2, 3)",
            "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE amount > (SELECT AVG(amount) FROM orders))",
            "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE order_date > (SELECT MAX(created_date) FROM logs WHERE user_id = users.id))"
        };

        for (String sql : nestedSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testCaseInsensitive() {
        System.out.println("6. 测试大小写不敏感:");

        String[] caseSqls = {
            "select * from users",
            "SELECT * FROM users",
            "Select * From users",
            "SeLeCt * FrOm users"
        };

        for (String sql : caseSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testWhitespaceHandling() {
        System.out.println("7. 测试空白字符处理:");

        String[] whitespaceSqls = {
            "  SELECT * FROM users  ",
            "\tSELECT\t*\tFROM\tusers",
            "\nSELECT\n*\nFROM\nusers",
            "SELECT * FROM users WHERE name = 'John' AND age > 18"
        };

        for (String sql : whitespaceSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }

    private static void testPerformance() {
        System.out.println("8. 性能测试:");

        // 构建一个复杂的SQL语句
        StringBuilder complexSql = new StringBuilder();
        complexSql.append("SELECT u.id, u.name, u.email, ");
        complexSql.append("(SELECT COUNT(*) FROM orders o WHERE o.user_id = u.id) as order_count, ");
        complexSql.append("(SELECT AVG(amount) FROM orders o WHERE o.user_id = u.id) as avg_amount ");
        complexSql.append("FROM users u ");
        complexSql.append("WHERE u.status = 'active' ");
        complexSql.append("AND u.id IN (SELECT user_id FROM user_roles WHERE role = 'customer') ");
        complexSql.append("AND u.created_date > (SELECT MIN(created_date) FROM users) ");
        complexSql.append("ORDER BY u.name ASC");

        String sql = complexSql.toString();

        // 性能测试
        long startTime = System.nanoTime();
        boolean result = SqlUtil.isValidSelectSql(sql);
        long endTime = System.nanoTime();

        long duration = (endTime - startTime) / 1_000_000; // 转换为毫秒

        System.out.println("  复杂SQL验证耗时: " + duration + "ms");
        System.out.println("  SQL长度: " + sql.length() + " 字符");
        System.out.println("  验证结果: " + (result ? "通过" : "失败"));
        System.out.println();
    }

    private static void testEdgeCases() {
        System.out.println("9. 边界条件测试:");

        String[] edgeSqls = {
            "SELECT 1",
            "SELECT *",
            "SELECT * FROM `users` WHERE `name` = 'test'",
            "SELECT * FROM [users] WHERE [name] = 'test'",
            "SELECT * FROM users -- This is a comment",
            "SELECT * FROM users /* This is a comment */"
        };

        for (String sql : edgeSqls) {
            boolean result = SqlUtil.isValidSelectSql(sql);
            System.out.printf("  ✓ %s -> %s%n", sql, result ? "通过" : "失败");
        }
        System.out.println();
    }
}
