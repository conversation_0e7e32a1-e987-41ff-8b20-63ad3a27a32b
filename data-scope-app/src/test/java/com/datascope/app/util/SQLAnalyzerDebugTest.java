package com.datascope.app.util;

import com.datascope.app.entity.Datasource;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class SQLAnalyzerDebugTest {

    @Mock
    private TableMapper tableMapper;

    @Mock
    private ColumnMapper columnMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private SQLAnalyzer sqlAnalyzer;

    private Datasource datasource;

    @BeforeEach
    void setUp() {
        datasource = new Datasource();
        datasource.setId("test-datasource-id");
    }

    @Test
    void debugFieldParsing() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            // 测试字段解析的每一步
            String testSql = "SELECT d.doc_no AS docname, p.title AS page_name FROM tbl_doc d JOIN tbl_doc_page p ON d.doc_no = p.doc_no";
            
            System.out.println("=== 调试字段解析过程 ===");
            System.out.println("SQL: " + testSql);
            
            // 1. 测试字段表达式解析
            java.lang.reflect.Method parseMethod = SQLAnalyzer.class.getDeclaredMethod("parseFieldExpressions", String.class);
            parseMethod.setAccessible(true);
            
            String fieldsPart = "d.doc_no AS docname, p.title AS page_name";
            @SuppressWarnings("unchecked")
            List<String> fieldExpressions = (List<String>) parseMethod.invoke(analyzer, fieldsPart);
            
            System.out.println("\n1. 解析出的字段表达式:");
            for (int i = 0; i < fieldExpressions.size(); i++) {
                System.out.println("  [" + i + "] " + fieldExpressions.get(i));
            }
            
            // 2. 测试每个字段的别名处理
            System.out.println("\n2. 字段别名处理:");
            for (String field : fieldExpressions) {
                System.out.println("\n处理字段: " + field);
                
                String originalField = field;
                String fieldLabel = field;
                
                // 处理字段别名
                if (field.toLowerCase().contains(" as ")) {
                    String[] parts = field.split("(?i) as ");
                    originalField = parts[0].trim();
                    if (parts.length > 1) {
                        fieldLabel = parts[1].trim().replaceAll("['\"`]", "");
                    }
                }
                
                System.out.println("  原始字段: " + originalField);
                System.out.println("  字段别名: " + fieldLabel);
                
                // 处理表名.字段名的形式
                String fieldName = originalField;
                if (fieldName.contains(".")) {
                    String[] parts = fieldName.split("\\.");
                    if (parts.length == 2) {
                        fieldName = parts[1];
                    }
                }
                
                System.out.println("  提取的字段名: " + fieldName);
                System.out.println("  最终应该使用的label: " + fieldLabel);
                
                // 验证逻辑
                if (field.equals("d.doc_no AS docname")) {
                    if (!fieldLabel.equals("docname")) {
                        System.err.println("❌ 错误：d.doc_no AS docname 的别名应该是 docname，但得到了 " + fieldLabel);
                    } else {
                        System.out.println("  ✓ 正确：别名是 " + fieldLabel);
                    }
                }
                
                if (field.equals("p.title AS page_name")) {
                    if (!fieldLabel.equals("page_name")) {
                        System.err.println("❌ 错误：p.title AS page_name 的别名应该是 page_name，但得到了 " + fieldLabel);
                    } else {
                        System.out.println("  ✓ 正确：别名是 " + fieldLabel);
                    }
                }
            }
            
            System.out.println("\n=== 调试结束 ===");
            
        } catch (Exception e) {
            System.err.println("调试过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
