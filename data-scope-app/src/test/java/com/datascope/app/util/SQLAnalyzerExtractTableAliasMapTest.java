package com.datascope.app.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SQLAnalyzer.extractTableAliasMap方法的单元测试
 */
@ExtendWith(MockitoExtension.class)
class SQLAnalyzerExtractTableAliasMapTest {

    @Mock
    private TableMapper tableMapper;

    @Mock
    private ColumnMapper columnMapper;

    @Mock
    private ObjectMapper objectMapper;

    private SQLAnalyzer sqlAnalyzer;
    private Method extractTableAliasMapMethod;

    @BeforeEach
    void setUp() throws Exception {
        sqlAnalyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);

        // 使用反射获取私有方法
        extractTableAliasMapMethod = SQLAnalyzer.class.getDeclaredMethod("extractTableAliasMap", String.class);
        extractTableAliasMapMethod.setAccessible(true);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> callExtractTableAliasMap(String sql) throws Exception {
        return (Map<String, String>) extractTableAliasMapMethod.invoke(sqlAnalyzer, sql);
    }

    @Test
    void testSingleTableWithoutAlias() throws Exception {
        String sql = "SELECT * FROM users WHERE id = 1";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(1, result.size());
        assertEquals("users", result.get("users"));
    }

    @Test
    void testSingleTableWithAlias() throws Exception {
        String sql = "SELECT * FROM users u WHERE u.id = 1";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(1, result.size());
        assertEquals("users", result.get("u"));
    }

    @Test
    void testSingleTableWithAsAlias() throws Exception {
        String sql = "SELECT * FROM users AS u WHERE u.id = 1";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(1, result.size());
        assertEquals("users", result.get("u"));
    }

    @Test
    void testMultipleTablesWithComma() throws Exception {
        String sql = "SELECT * FROM TBL_NOTIFY_RULE rule, TBL_NOTIFY_RECORD notify WHERE rule.id = notify.rule_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("TBL_NOTIFY_RULE", result.get("rule"));
        assertEquals("TBL_NOTIFY_RECORD", result.get("notify"));
    }

    @Test
    void testMultipleTablesWithCommaAndMixedAliases() throws Exception {
        String sql = "SELECT * FROM users u, products, orders o WHERE u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(3, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("products", result.get("products"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testDatabasePrefixedTable() throws Exception {
        String sql = "SELECT * FROM db1.users u, db2.products p WHERE u.id = p.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("products", result.get("p"));
    }

    @Test
    void testInnerJoin() throws Exception {
        String sql = "SELECT * FROM users u INNER JOIN orders o ON u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testLeftJoin() throws Exception {
        String sql = "SELECT * FROM users u LEFT JOIN orders o ON u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testRightJoin() throws Exception {
        String sql = "SELECT * FROM users u RIGHT JOIN orders o ON u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testMultipleJoins() throws Exception {
        String sql = "SELECT * FROM users u " +
                    "LEFT JOIN orders o ON u.id = o.user_id " +
                    "INNER JOIN products p ON o.product_id = p.id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(3, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
        assertEquals("products", result.get("p"));
    }

    @Test
    void testJoinWithoutAlias() throws Exception {
        String sql = "SELECT * FROM users JOIN orders ON users.id = orders.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("users"));
        assertEquals("orders", result.get("orders"));
    }

    @Test
    void testJoinWithAsKeyword() throws Exception {
        String sql = "SELECT * FROM users AS u JOIN orders AS o ON u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testComplexSqlWithMixedFromAndJoin() throws Exception {
        String sql = "SELECT * FROM users u, products p " +
                    "LEFT JOIN orders o ON u.id = o.user_id " +
                    "INNER JOIN order_items oi ON o.id = oi.order_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(4, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("products", result.get("p"));
        assertEquals("orders", result.get("o"));
        assertEquals("order_items", result.get("oi"));
    }

    @Test
    void testSqlWithWhereClause() throws Exception {
        String sql = "SELECT * FROM users u, orders o WHERE u.id = o.user_id AND u.status = 'active'";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testSqlWithGroupBy() throws Exception {
        String sql = "SELECT u.name, COUNT(*) FROM users u, orders o WHERE u.id = o.user_id GROUP BY u.name";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testSqlWithOrderBy() throws Exception {
        String sql = "SELECT * FROM users u, orders o WHERE u.id = o.user_id ORDER BY u.name";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testSqlWithLimit() throws Exception {
        String sql = "SELECT * FROM users u, orders o WHERE u.id = o.user_id LIMIT 10";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testSqlWithWithClause() throws Exception {
        String sql = "SELECT * FROM users u, orders o WHERE u.id = o.user_id WITH ur";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testOriginalProblematicSql() throws Exception {
        String sql = "SELECT notify.ID,notify.RECORD_CONTENT_TYPE,rule.HANDLER,rule.HANDLER_CHAR_SET,rule.HANDLER_PARAMS,rule.RULE_NAME,notify.RULE_CODE,notify.MERCHANT_NO,notify.MERCHANT_ORDER,notify.URL,notify.CONTENT,notify.STATUS,notify.TOTAL_TIMES,notify.FAIL_TIMES,notify.LAST_FAIL_CODE,notify.LAST_FAIL_MSG,notify.LAST_FAIL_TIME,notify.CREATE_TIME,notify.SUCCESS_TIME,notify.HTTP_METHOD,notify.HANDLERCHARSET,notify.CONFIG_EXTENSION " +
                    "from  TBL_NOTIFY_RULE rule,TBL_NOTIFY_RECORD notify " +
                    "where  notify.RULE_CODE = rule.RULE_CODE " +
                    "and notify.MERCHANT_NO like '10012465470%' " +
                    "and notify.STATUS = 'FAIL' " +
                    "and notify.CREATE_TIME >= TIMESTAMP('2025-06-26 00:00:00') " +
                    "and notify.CREATE_TIME <= TIMESTAMP('2025-06-27 00:00:00') " +
                    "with ur";

        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("TBL_NOTIFY_RULE", result.get("rule"));
        assertEquals("TBL_NOTIFY_RECORD", result.get("notify"));
    }

    @Test
    void testCaseInsensitive() throws Exception {
        String sql = "select * from Users u, Orders o where u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("Users", result.get("u"));
        assertEquals("Orders", result.get("o"));
    }

    @Test
    void testMultilineSQL() throws Exception {
        String sql = "SELECT *\n" +
                    "FROM users u,\n" +
                    "     orders o\n" +
                    "WHERE u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testExtraWhitespace() throws Exception {
        String sql = "SELECT   *   FROM    users   u   ,   orders   o   WHERE   u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testEmptySQL() throws Exception {
        String sql = "";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertTrue(result.isEmpty());
    }

    @Test
    void testNullSQL() throws Exception {
        String sql = null;
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertTrue(result.isEmpty());
    }

    @Test
    void testSQLWithoutFromClause() throws Exception {
        String sql = "SELECT 1 as one";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertTrue(result.isEmpty());
    }

    @Test
    void testSQLWithSubquery() throws Exception {
        String sql = "SELECT * FROM (SELECT * FROM users) u, orders o WHERE u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        // 当前实现不支持子查询，但不应该崩溃
        assertNotNull(result);
    }

    @Test
    void testDatabasePrefixWithJoin() throws Exception {
        String sql = "SELECT * FROM db1.users u LEFT JOIN db2.orders o ON u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testManyTablesWithComma() throws Exception {
        String sql = "SELECT * FROM table1 t1, table2 t2, table3 t3, table4 t4 WHERE t1.id = t2.id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(4, result.size());
        assertEquals("table1", result.get("t1"));
        assertEquals("table2", result.get("t2"));
        assertEquals("table3", result.get("t3"));
        assertEquals("table4", result.get("t4"));
    }

    @Test
    void testMixedCaseKeywords() throws Exception {
        String sql = "Select * From Users u Left Join Orders o On u.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("Users", result.get("u"));
        assertEquals("Orders", result.get("o"));
    }

    @Test
    void testTabsAndNewlines() throws Exception {
        String sql = "SELECT\t*\nFROM\tusers\tu,\n\torders\to\nWHERE\tu.id = o.user_id";
        Map<String, String> result = callExtractTableAliasMap(sql);

        assertEquals(2, result.size());
        assertEquals("users", result.get("u"));
        assertEquals("orders", result.get("o"));
    }

    @Test
    void testDebugJoinRegex() throws Exception {
        String sql = "SELECT * FROM users u LEFT JOIN orders o ON u.id = o.user_id";

        // Debug the JOIN regex
        Pattern joinPattern = Pattern.compile(
            "\\b(?:LEFT|RIGHT|INNER)?\\s*JOIN\\s+((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?(?:\\s+ON|\\s+WHERE|\\s+GROUP|\\s+ORDER|\\s+LIMIT|\\s+WITH|\\s*$)",
            Pattern.CASE_INSENSITIVE
        );
        Matcher joinMatcher = joinPattern.matcher(sql);

        System.out.println("SQL: " + sql);
        while (joinMatcher.find()) {
            System.out.println("Found match: " + joinMatcher.group());
            System.out.println("Table name: " + joinMatcher.group(1));
            System.out.println("Alias: " + joinMatcher.group(2));
        }

        Map<String, String> result = callExtractTableAliasMap(sql);
        System.out.println("Result: " + result);

        // This test is just for debugging, so we don't assert anything
    }

    @Test
    void testDebugFromRegex() throws Exception {
        String sql = "SELECT * FROM users u LEFT JOIN orders o ON u.id = o.user_id";

        // Debug the FROM regex
        Pattern fromClausePattern = Pattern.compile(
            "\\bFROM\\s+([^\\s]+.*?)(?:\\s+WHERE|\\s+GROUP\\s+BY|\\s+ORDER\\s+BY|\\s+LIMIT|\\s+JOIN|\\s+WITH|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        Matcher fromClauseMatcher = fromClausePattern.matcher(sql);

        System.out.println("SQL: " + sql);
        if (fromClauseMatcher.find()) {
            String fromClause = fromClauseMatcher.group(1).trim();
            System.out.println("FROM clause: '" + fromClause + "'");

            // 按逗号分割多个表
            String[] tables = fromClause.split(",");
            System.out.println("Tables count: " + tables.length);

            for (int i = 0; i < tables.length; i++) {
                String tableExpr = tables[i].trim();
                System.out.println("Table expression " + i + ": '" + tableExpr + "'");

                // 对每个表表达式使用原来的正则表达式逻辑
                Pattern tablePattern = Pattern.compile(
                    "^((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?$",
                    Pattern.CASE_INSENSITIVE
                );
                Matcher tableMatcher = tablePattern.matcher(tableExpr);

                if (tableMatcher.find()) {
                    String fullTableName = tableMatcher.group(1);
                    String alias = tableMatcher.group(2);
                    System.out.println("  Full table name: " + fullTableName);
                    System.out.println("  Alias: " + alias);
                } else {
                    System.out.println("  No match for table pattern");
                }
            }
        } else {
            System.out.println("No FROM clause found");
        }

        Map<String, String> result = callExtractTableAliasMap(sql);
        System.out.println("Result: " + result);

        // This test is just for debugging, so we don't assert anything
    }
}
