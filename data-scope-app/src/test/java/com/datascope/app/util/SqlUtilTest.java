package com.datascope.app.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * SqlUtil测试类
 * <AUTHOR>
 */
@DisplayName("SqlUtil测试")
public class SqlUtilTest {

    @Test
    @DisplayName("测试有效的SELECT语句")
    public void testValidSelectStatements() {
        // 基本SELECT语句
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users"));
        assertTrue(SqlUtil.isValidSelectSql("select id, name from users"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT id, name FROM users WHERE id = 1"));
        
        // 带条件的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE age > 18"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT id, name FROM users WHERE status = 'active'"));
        
        // 带JOIN的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT u.id, u.name, o.order_id FROM users u JOIN orders o ON u.id = o.user_id"));
        
        // 带子查询的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE id IN (SELECT user_id FROM orders)"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT id, name, (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count FROM users"));
        
        // 带GROUP BY的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT department, COUNT(*) as count FROM employees GROUP BY department"));
        
        // 带ORDER BY的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users ORDER BY name ASC"));
        
        // 带LIMIT的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users LIMIT 10"));
        
        // 复杂嵌套查询
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE amount > (SELECT AVG(amount) FROM orders))"));
    }

    @Test
    @DisplayName("测试无效的SQL语句")
    public void testInvalidSqlStatements() {
        // 非SELECT语句
        assertFalse(SqlUtil.isValidSelectSql("INSERT INTO users (id, name) VALUES (1, 'test')"));
        assertFalse(SqlUtil.isValidSelectSql("UPDATE users SET name = 'test' WHERE id = 1"));
        assertFalse(SqlUtil.isValidSelectSql("DELETE FROM users WHERE id = 1"));
        assertFalse(SqlUtil.isValidSelectSql("CREATE TABLE users (id INT, name VARCHAR(50))"));
        assertFalse(SqlUtil.isValidSelectSql("DROP TABLE users"));
        assertFalse(SqlUtil.isValidSelectSql("ALTER TABLE users ADD COLUMN age INT"));
        assertFalse(SqlUtil.isValidSelectSql("TRUNCATE TABLE users"));
        
        // 包含其他SQL关键字
        assertFalse(SqlUtil.isValidSelectSql("SELECT * FROM users; INSERT INTO logs VALUES (1)"));
        assertFalse(SqlUtil.isValidSelectSql("SELECT * FROM users; UPDATE users SET name = 'test'"));
        
        // 空值或null
        assertFalse(SqlUtil.isValidSelectSql(""));
        assertFalse(SqlUtil.isValidSelectSql("   "));
        assertFalse(SqlUtil.isValidSelectSql(null));
    }

    @Test
    @DisplayName("测试多语句SQL")
    public void testMultipleStatements() {
        // 多个SELECT语句（应该被拒绝）
        assertFalse(SqlUtil.isValidSelectSql("SELECT * FROM users; SELECT * FROM orders"));
        assertFalse(SqlUtil.isValidSelectSql("SELECT id FROM users; SELECT name FROM users"));
        
        // 单个SELECT语句（应该通过）
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users;"));
    }

    @Test
    @DisplayName("测试字符串字面量处理")
    public void testStringLiterals() {
        // 包含字符串字面量的SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE name = 'John'"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE description = \"This is a test\""));
        
        // 字符串中包含SQL关键字（应该被忽略）
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE description = 'This is an INSERT test'"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE comment = 'UPDATE this record'"));
        
        // 转义字符
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE name = 'O\\'Connor'"));
    }

    @Test
    @DisplayName("测试括号嵌套")
    public void testParenthesesNesting() {
        // 简单括号
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE id IN (1, 2, 3)"));
        
        // 嵌套括号
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE amount > (SELECT AVG(amount) FROM orders))"));
        
        // 复杂嵌套
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE order_date > (SELECT MAX(created_date) FROM logs WHERE user_id = users.id))"));
    }

    @Test
    @DisplayName("测试大小写不敏感")
    public void testCaseInsensitive() {
        assertTrue(SqlUtil.isValidSelectSql("select * from users"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users"));
        assertTrue(SqlUtil.isValidSelectSql("Select * From users"));
        assertTrue(SqlUtil.isValidSelectSql("SeLeCt * FrOm users"));
    }

    @Test
    @DisplayName("测试空白字符处理")
    public void testWhitespaceHandling() {
        assertTrue(SqlUtil.isValidSelectSql("  SELECT * FROM users  "));
        assertTrue(SqlUtil.isValidSelectSql("\tSELECT\t*\tFROM\tusers"));
        assertTrue(SqlUtil.isValidSelectSql("\nSELECT\n*\nFROM\nusers"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users WHERE name = 'John' AND age > 18"));
    }

    @Test
    @DisplayName("性能测试")
    public void testPerformance() {
        // 构建一个复杂的SQL语句
        StringBuilder complexSql = new StringBuilder();
        complexSql.append("SELECT u.id, u.name, u.email, ");
        complexSql.append("(SELECT COUNT(*) FROM orders o WHERE o.user_id = u.id) as order_count, ");
        complexSql.append("(SELECT AVG(amount) FROM orders o WHERE o.user_id = u.id) as avg_amount ");
        complexSql.append("FROM users u ");
        complexSql.append("WHERE u.status = 'active' ");
        complexSql.append("AND u.id IN (SELECT user_id FROM user_roles WHERE role = 'customer') ");
        complexSql.append("AND u.created_date > (SELECT MIN(created_date) FROM users) ");
        complexSql.append("ORDER BY u.name ASC");

        String sql = complexSql.toString();
        
        // 性能测试
        long startTime = System.nanoTime();
        boolean result = SqlUtil.isValidSelectSql(sql);
        long endTime = System.nanoTime();
        
        long duration = (endTime - startTime) / 1_000_000; // 转换为毫秒
        
        System.out.println("复杂SQL验证耗时: " + duration + "ms");
        System.out.println("SQL长度: " + sql.length() + " 字符");
        System.out.println("验证结果: " + result);
        
        // 断言结果正确且性能可接受
        assertTrue(result);
        assertTrue(duration < 10, "性能测试失败，耗时超过10ms: " + duration + "ms");
    }

    @Test
    @DisplayName("边界条件测试")
    public void testEdgeCases() {
        // 最短的有效SELECT
        assertTrue(SqlUtil.isValidSelectSql("SELECT 1"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT *"));
        
        // 包含特殊字符
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM `users` WHERE `name` = 'test'"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM [users] WHERE [name] = 'test'"));
        
        // 包含注释（应该被忽略）
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users -- This is a comment"));
        assertTrue(SqlUtil.isValidSelectSql("SELECT * FROM users /* This is a comment */"));
    }

    @Test
    @DisplayName("测试removeSemicolon方法")
    public void testRemoveSemicolon() {
        assertEquals("SELECT * FROM users", SqlUtil.removeSemicolon("SELECT * FROM users;"));
        assertEquals("SELECT * FROM users", SqlUtil.removeSemicolon("SELECT * FROM users"));
        assertEquals("", SqlUtil.removeSemicolon(""));
        assertNull(SqlUtil.removeSemicolon(null));
    }
} 