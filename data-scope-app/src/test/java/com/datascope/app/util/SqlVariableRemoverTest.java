package com.datascope.app.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SQL变量移除工具测试类
 */
public class SqlVariableRemoverTest {

    @Test
    @DisplayName("测试检测SQL中是否包含变量 - 包含#{变量}格式")
    public void testContainsVariables_MybatisFormat() {
        String sql = "SELECT * FROM users WHERE name = #{userName} AND status = 'active'";
        assertTrue(SqlVariableRemover.containsVariables(sql));
    }

    @Test
    @DisplayName("测试检测SQL中是否包含变量 - 包含:变量格式")
    public void testContainsVariables_ColonFormat() {
        String sql = "SELECT * FROM users WHERE name = :userName AND status = 'active'";
        assertTrue(SqlVariableRemover.containsVariables(sql));
    }

    @Test
    @DisplayName("测试检测SQL中是否包含变量 - 不包含变量")
    public void testContainsVariables_NoVariables() {
        String sql = "SELECT * FROM users WHERE status = 'active'";
        assertFalse(SqlVariableRemover.containsVariables(sql));
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 第一类SQL示例")
    public void testRemoveVariables_FirstExample() {
        String sql = "SELECT * from queries qu LEFT JOIN query_parameters qv on qu.id = qv.query_id " +
                "and qu.name = #{name} and qu.service_status = 'ENABLED' " +
                "where qu.status = #{statue} and qv.type = 'string'";

        String expected = "SELECT * from queries qu LEFT JOIN query_parameters qv on qu.id = qv.query_id " +
                "AND qu.service_status = 'ENABLED' " +
                "WHERE qv.type = 'string'";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 第二类SQL示例")
    public void testRemoveVariables_SecondExample() {
        String sql = "SELECT * from queries qu where qv.data = '2025-12-12'";
        String expected = "SELECT * from queries qu where qv.data = '2025-12-12'";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 第三类SQL示例")
    public void testRemoveVariables_ThirdExample() {
        String sql = "SELECT * \n" +
                "FROM test_query_users \n" +
                "WHERE (#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%'))\n" +
                "AND register_time >= #{startDate}\n" +
                "AND register_time <= #{endDate}\n" +
                "ORDER BY register_time DESC";

        String expected = "SELECT * FROM test_query_users ORDER BY register_time DESC";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 第四类SQL示例(MyBatis标签)")
    public void testRemoveVariables_FourthExample() {
        String sql = "select * from test \n" +
                "<where>\n" +
                "<if test=\"TYPENAME != null and TYPENAME != ''\">\n" +
                " TYPENAME = #{TYPENAME} \n" +
                " </if>\n" +
                "<if test=\"PAYMENT_TYPE2 != null and PAYMENT_TYPE2 != ''\">\n" +
                " and PAYMENT_TYPE2 = #{PAYMENT_TYPE2}\n" +
                " </if>  \n" +
                " and CRETM = #{CRETM}\n" +
                " and name like concat('%', '我', '%')\n" +
                " </where>\n" +
                "  order by sysconid desc";

        String expected = "select * from test WHERE name like concat('%', '我', '%') order by sysconid desc";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 复杂条件")
    public void testRemoveVariables_ComplexConditions() {
        String sql = "SELECT u.id, u.name, u.email, d.name as department_name\n" +
                "FROM users u\n" +
                "JOIN departments d ON u.department_id = d.id\n" +
                "WHERE u.status2 = 'active'\n" +
                "AND (u.role = #{role} OR u.permission_level >= #{minPermission})\n" +
                "AND u.created_at BETWEEN #{startDate} AND #{endDate}\n" +
                "AND d.region = 'APAC'\n" +
                "AND d.die = 'dsdas'\n" +
                "ORDER BY u.name ASC";

        String expected = "SELECT u.id, u.name, u.email, d.name as department_name FROM users u JOIN departments d ON u.department_id = d.id WHERE u.status2 = 'active' AND d.region = 'APAC' AND d.die = 'dsdas' ORDER BY u.name ASC";
        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 嵌套条件")
    public void testRemoveVariables_NestedConditions() {
        String sql = "SELECT * FROM products\n" +
                "WHERE category_id IN (SELECT id FROM categories WHERE name = #{categoryName})\n" +
                "AND price BETWEEN #{minPrice} AND #{maxPrice}\n" +
                "AND status = 'available'\n" +
                "ORDER BY created_at DESC";

        String expected = "SELECT * FROM products WHERE status = 'available' ORDER BY created_at DESC";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 多个if标签嵌套")
    public void testRemoveVariables_NestedIfTags() {
        String sql = "SELECT * FROM orders\n" +
                "<where>\n" +
                "  <if test=\"orderId != null\">\n" +
                "    order_id = #{orderId}\n" +
                "  </if>\n" +
                "  <if test=\"status != null\">\n" +
                "    AND status = #{status}\n" +
                "  </if>\n" +
                "  AND created_at > '2023-01-01'\n" +
                "  <if test=\"customerId != null\">\n" +
                "    AND customer_id = #{customerId}\n" +
                "  </if>\n" +
                "</where>\n" +
                "ORDER BY created_at DESC";

        String expected = "SELECT * FROM orders WHERE created_at > '2023-01-01' ORDER BY created_at DESC";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("嵌套")
    public void testRemoveVariables_ten() {
        String sql = "SELECT * \n" +
            "FROM test_query_users_2 \n" +
            "WHERE \n" +
            "(#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%'))\n" +
            "AND (#{userName} IS NULL OR username LIKE CONCAT('%', #{userName}, '%'))\n" +
            "AND (#{gender} IS NULL OR gender in(#{gender}))\n" +
            "AND (#{startAge} IS NULL OR age >= #{startAge}) \n" +
            "AND (#{endAge} IS NULL OR age <= #{endAge}) \n" +
            "AND  (#{startDate} IS NULL OR register_time >= #{startDate})\n" +
            "AND  (#{endDate} IS NULL OR  register_time <= #{endDate})";

        String expected = "SELECT * FROM test_query_users_2";
        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("嵌套")
    public void testRemoveVariables_eleven() {
        String sql = "select *\n" +
                "from datasources\n" +
                "where name like '%#{data}%' and host=#{host} and ip between #{startDate} and #{endDate}";

        String expected = "select * from datasources";
        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("测试移除SQL中的变量 - 带时分秒日期示例")
    public void testRemoveVariables_DateExample() {
        String sql = "SELECT notify.ID,notify.RECORD_CONTENT_TYPE,rule.HANDLER,rule.HANDLER_CHAR_SET,rule.HANDLER_PARAMS,rule.RULE_NAME,notify.RULE_CODE," +
            "notify.MERCHANT_NO,notify.MERCHANT_ORDER,notify.URL,notify.CONTENT,notify.STATUS,notify.TOTAL_TIMES,notify.FAIL_TIMES," +
            "notify.LAST_FAIL_CODE,notify.LAST_FAIL_MSG,notify.LAST_FAIL_TIME,notify.CREATE_TIME,notify.SUCCESS_TIME," +
            "notify.HTTP_METHOD,notify.HANDLERCHARSET,notify.CONFIG_EXTENSION from TBL_NOTIFY_RULE rule,TBL_NOTIFY_RECORD " +
            "notify where notify.RULE_CODE = rule.RULE_CODE and notify.MERCHANT_NO like '10012465470%' and notify.STATUS = 'FAIL' " +
            "and notify.CREATE_TIME >= TIMESTAMP('2025-06-26 00:00:00') and notify.CREATE_TIME <= TIMESTAMP('2025-06-27 00:00:00') " +
            "with ur";

        String result = SqlVariableRemover.removeVariables(sql);
        System.out.println(result);
    }
}
