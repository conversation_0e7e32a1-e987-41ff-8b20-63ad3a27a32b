package com.datascope.app.util;

import com.datascope.app.dto.query.QueryFieldDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SQLAnalyzerTest {

    @Mock
    private TableMapper tableMapper;

    @Mock
    private ColumnMapper columnMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private SQLAnalyzer sqlAnalyzer;

    private Datasource datasource;

    @BeforeEach
    void setUp() {
        datasource = new Datasource();
        datasource.setId("test-datasource-id");
    }

    @Test
    void testParseFieldExpressions_SimpleFields() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = SQLAnalyzer.class.getDeclaredMethod("parseFieldExpressions", String.class);
            method.setAccessible(true);
            
            String fieldsPart = "d.doc_no, p.title, p.page_no";
            @SuppressWarnings("unchecked")
            List<String> result = (List<String>) method.invoke(analyzer, fieldsPart);
            
            assertEquals(3, result.size());
            assertEquals("d.doc_no", result.get(0));
            assertEquals("p.title", result.get(1));
            assertEquals("p.page_no", result.get(2));
        } catch (Exception e) {
            fail("Failed to test parseFieldExpressions: " + e.getMessage());
        }
    }

    @Test
    void testParseFieldExpressions_WithFunctions() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            java.lang.reflect.Method method = SQLAnalyzer.class.getDeclaredMethod("parseFieldExpressions", String.class);
            method.setAccessible(true);
            
            String fieldsPart = "d.doc_no AS docname, p.title AS page_name, COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            @SuppressWarnings("unchecked")
            List<String> result = (List<String>) method.invoke(analyzer, fieldsPart);
            
            assertEquals(3, result.size());
            assertEquals("d.doc_no AS docname", result.get(0));
            assertEquals("p.title AS page_name", result.get(1));
            assertEquals("COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink", result.get(2));
        } catch (Exception e) {
            fail("Failed to test parseFieldExpressions with functions: " + e.getMessage());
        }
    }

    @Test
    void testExtractFieldNamesFromExpression_SimpleField() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            java.lang.reflect.Method method = SQLAnalyzer.class.getDeclaredMethod("extractFieldNamesFromExpression", String.class);
            method.setAccessible(true);
            
            String expression = "d.doc_no";
            @SuppressWarnings("unchecked")
            List<String> result = (List<String>) method.invoke(analyzer, expression);
            
            assertEquals(1, result.size());
            assertEquals("doc_no", result.get(0));
        } catch (Exception e) {
            fail("Failed to test extractFieldNamesFromExpression: " + e.getMessage());
        }
    }

    @Test
    void testExtractFieldNamesFromExpression_ComplexFunction() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            java.lang.reflect.Method method = SQLAnalyzer.class.getDeclaredMethod("extractFieldNamesFromExpression", String.class);
            method.setAccessible(true);
            
            String expression = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            @SuppressWarnings("unchecked")
            List<String> result = (List<String>) method.invoke(analyzer, expression);
            
            assertEquals(3, result.size());
            assertTrue(result.contains("ref_url"));
            assertTrue(result.contains("doc_no"));
            assertTrue(result.contains("page_no"));
        } catch (Exception e) {
            fail("Failed to test extractFieldNamesFromExpression with complex function: " + e.getMessage());
        }
    }

    @Test
    void testIsSqlKeywordOrFunction() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);

        try {
            java.lang.reflect.Method method = SQLAnalyzer.class.getDeclaredMethod("isSqlKeywordOrFunction", String.class);
            method.setAccessible(true);

            assertTrue((Boolean) method.invoke(analyzer, "SELECT"));
            assertTrue((Boolean) method.invoke(analyzer, "COALESCE"));
            assertTrue((Boolean) method.invoke(analyzer, "CONCAT"));
            assertFalse((Boolean) method.invoke(analyzer, "doc_no"));
            assertFalse((Boolean) method.invoke(analyzer, "ref_url"));
        } catch (Exception e) {
            fail("Failed to test isSqlKeywordOrFunction: " + e.getMessage());
        }
    }

    @Test
    void testRealWorldComplexSQL() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);

        try {
            // 测试字段表达式解析
            java.lang.reflect.Method parseMethod = SQLAnalyzer.class.getDeclaredMethod("parseFieldExpressions", String.class);
            parseMethod.setAccessible(true);

            String complexFieldsPart = "d.doc_no AS docname, p.title AS page_name, COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            @SuppressWarnings("unchecked")
            List<String> fieldExpressions = (List<String>) parseMethod.invoke(analyzer, complexFieldsPart);

            assertEquals(3, fieldExpressions.size());
            assertEquals("d.doc_no AS docname", fieldExpressions.get(0));
            assertEquals("p.title AS page_name", fieldExpressions.get(1));
            assertEquals("COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink", fieldExpressions.get(2));

            // 测试复杂函数表达式的字段提取
            java.lang.reflect.Method extractMethod = SQLAnalyzer.class.getDeclaredMethod("extractFieldNamesFromExpression", String.class);
            extractMethod.setAccessible(true);

            String complexExpression = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            @SuppressWarnings("unchecked")
            List<String> extractedFields = (List<String>) extractMethod.invoke(analyzer, complexExpression);

            // 验证提取出的实际数据库字段名
            assertEquals(3, extractedFields.size());
            assertTrue(extractedFields.contains("ref_url"), "应该提取出 ref_url 字段");
            assertTrue(extractedFields.contains("doc_no"), "应该提取出 doc_no 字段");
            assertTrue(extractedFields.contains("page_no"), "应该提取出 page_no 字段");

            System.out.println("从复杂表达式中提取到的实际字段: " + extractedFields);

            // 测试简单字段表达式
            String simpleExpression = "d.doc_no AS docname";
            @SuppressWarnings("unchecked")
            List<String> simpleFields = (List<String>) extractMethod.invoke(analyzer, simpleExpression);
            assertEquals(1, simpleFields.size());
            assertEquals("doc_no", simpleFields.get(0));

        } catch (Exception e) {
            fail("Failed to test real world complex SQL: " + e.getMessage());
        }
    }

    @Test
    void testExtractFieldsIntegration() {
        // 这个测试验证整个extractFields方法的行为
        // 注意：这个测试不会真正查询数据库，因为我们mock了mapper

        try {
            // 模拟一个简单的SQL来测试字段解析逻辑
            String testSql = "SELECT d.doc_no AS docname, p.title AS page_name, COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink FROM tbl_doc d JOIN tbl_doc_page p ON d.doc_no = p.doc_no";

            // 测试表别名映射提取
            java.lang.reflect.Method aliasMethod = SQLAnalyzer.class.getDeclaredMethod("extractTableAliasMap", String.class);
            aliasMethod.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<String, String> aliasMap = (Map<String, String>) aliasMethod.invoke(sqlAnalyzer, testSql);

            System.out.println("提取到的表别名映射: " + aliasMap);

            // 验证表别名映射
            assertTrue(aliasMap.containsKey("d"), "应该包含表别名 d");
            assertTrue(aliasMap.containsKey("p"), "应该包含表别名 p");
            assertEquals("tbl_doc", aliasMap.get("d"));
            assertEquals("tbl_doc_page", aliasMap.get("p"));

        } catch (Exception e) {
            fail("Failed to test extractFields integration: " + e.getMessage());
        }
    }

    @Test
    void testFieldLabelWithAlias() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);

        try {
            // 测试有AS别名的字段的label处理
            java.lang.reflect.Method extractMethod = SQLAnalyzer.class.getDeclaredMethod("extractFieldNamesFromExpression", String.class);
            extractMethod.setAccessible(true);

            // 测试有AS别名的复杂表达式
            String expressionWithAlias = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            @SuppressWarnings("unchecked")
            List<String> fieldsWithAlias = (List<String>) extractMethod.invoke(analyzer, expressionWithAlias);

            assertEquals(3, fieldsWithAlias.size());
            assertTrue(fieldsWithAlias.contains("ref_url"));
            assertTrue(fieldsWithAlias.contains("doc_no"));
            assertTrue(fieldsWithAlias.contains("page_no"));

            // 测试没有AS别名的表达式
            String expressionWithoutAlias = "d.doc_no";
            @SuppressWarnings("unchecked")
            List<String> fieldsWithoutAlias = (List<String>) extractMethod.invoke(analyzer, expressionWithoutAlias);

            assertEquals(1, fieldsWithoutAlias.size());
            assertEquals("doc_no", fieldsWithoutAlias.get(0));

            System.out.println("有AS别名的表达式提取字段: " + fieldsWithAlias);
            System.out.println("无AS别名的表达式提取字段: " + fieldsWithoutAlias);

        } catch (Exception e) {
            fail("Failed to test field label with alias: " + e.getMessage());
        }
    }
}
