package com.datascope.app.util;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 专门测试SQLAnalyzer中label字段处理的测试类
 */
class SQLAnalyzerLabelTest {

    @Test
    void testLabelHandlingDemo() {
        System.out.println("=== SQLAnalyzer Label处理演示 ===");
        
        // 模拟不同类型的字段表达式
        String[] testExpressions = {
            "d.doc_no AS docname",
            "p.title AS page_name", 
            "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink",
            "p.ref_url",  // 没有AS别名
            "COUNT(*) AS total_count"
        };
        
        for (String expression : testExpressions) {
            System.out.println("\n原始表达式: " + expression);
            
            // 检查是否有AS别名
            boolean hasAlias = expression.toLowerCase().contains(" as ");
            System.out.println("是否有AS别名: " + hasAlias);
            
            if (hasAlias) {
                String[] parts = expression.split("(?i) as ");
                String originalField = parts[0].trim();
                String alias = parts[1].trim().replaceAll("['\"`]", "");
                
                System.out.println("原始字段表达式: " + originalField);
                System.out.println("AS别名: " + alias);
                System.out.println("应该使用的label: " + alias);
            } else {
                System.out.println("没有AS别名，label应该使用字段名本身");
            }
        }
        
        System.out.println("\n=== 演示结束 ===");
    }
    
    @Test
    void testComplexExpressionLabelLogic() {
        // 测试复杂表达式的label逻辑
        String complexExpression = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
        
        // 模拟extractFieldNamesFromExpression的结果
        List<String> extractedFields = Arrays.asList("ref_url", "doc_no", "page_no");
        
        // 检查是否有AS别名
        boolean hasAlias = complexExpression.toLowerCase().contains(" as ");
        assertTrue(hasAlias, "复杂表达式应该有AS别名");
        
        if (hasAlias) {
            String[] parts = complexExpression.split("(?i) as ");
            String alias = parts[1].trim().replaceAll("['\"`]", "");
            assertEquals("doclink", alias);
            
            // 对于有AS别名的复杂表达式，每个提取出的字段都应该使用这个别名作为label
            for (String fieldName : extractedFields) {
                System.out.println("字段名: " + fieldName + ", Label: " + alias);
                // 在实际代码中，每个QueryFieldDTO的label都会是"doclink"
            }
        }
    }

    @Test
    void testFieldDeduplicationLogic() {
        System.out.println("\n=== 字段去重逻辑演示 ===");

        // 模拟SQL: SELECT d.doc_no AS docname, p.title AS page_name, COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink
        String[] fieldExpressions = {
            "d.doc_no AS docname",
            "p.title AS page_name",
            "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink"
        };

        System.out.println("原始字段表达式:");
        for (String expr : fieldExpressions) {
            System.out.println("  " + expr);
        }

        System.out.println("\n期望的提取结果:");
        System.out.println("1. doc_no (来自 d.doc_no AS docname) - 别名: docname");
        System.out.println("2. title (来自 p.title AS page_name) - 别名: page_name");
        System.out.println("3. ref_url (来自复杂表达式中的 p.ref_url) - 别名: ref_url");
        System.out.println("4. page_no (来自复杂表达式中的 p.page_no) - 别名: page_no");
        System.out.println("注意: d.doc_no 在复杂表达式中出现，但因为已经在第1步处理过，所以跳过");

        // 模拟去重逻辑
        java.util.Set<String> processedFields = new java.util.HashSet<>();

        // 第一个表达式: d.doc_no AS docname
        String fieldKey1 = "tbl_doc.doc_no";
        if (!processedFields.contains(fieldKey1)) {
            processedFields.add(fieldKey1);
            System.out.println("\n✓ 添加字段: doc_no, 表: tbl_doc, 别名: docname");
        }

        // 第二个表达式: p.title AS page_name
        String fieldKey2 = "tbl_doc_page.title";
        if (!processedFields.contains(fieldKey2)) {
            processedFields.add(fieldKey2);
            System.out.println("✓ 添加字段: title, 表: tbl_doc_page, 别名: page_name");
        }

        // 第三个表达式的字段: p.ref_url
        String fieldKey3 = "tbl_doc_page.ref_url";
        if (!processedFields.contains(fieldKey3)) {
            processedFields.add(fieldKey3);
            System.out.println("✓ 添加字段: ref_url, 表: tbl_doc_page, 别名: ref_url");
        }

        // 第三个表达式的字段: d.doc_no (重复)
        String fieldKey4 = "tbl_doc.doc_no";
        if (!processedFields.contains(fieldKey4)) {
            processedFields.add(fieldKey4);
            System.out.println("✓ 添加字段: doc_no, 表: tbl_doc");
        } else {
            System.out.println("✗ 跳过重复字段: doc_no, 表: tbl_doc (已存在)");
        }

        // 第三个表达式的字段: p.page_no
        String fieldKey5 = "tbl_doc_page.page_no";
        if (!processedFields.contains(fieldKey5)) {
            processedFields.add(fieldKey5);
            System.out.println("✓ 添加字段: page_no, 表: tbl_doc_page, 别名: page_no");
        }

        System.out.println("\n最终处理的字段数量: " + processedFields.size());
        System.out.println("=== 演示结束 ===");
    }
}
