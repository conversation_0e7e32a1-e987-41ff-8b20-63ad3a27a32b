package com.datascope.app.util;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 专门测试SQLAnalyzer中label字段处理的测试类
 */
class SQLAnalyzerLabelTest {

    @Test
    void testLabelHandlingDemo() {
        System.out.println("=== SQLAnalyzer Label处理演示 ===");
        
        // 模拟不同类型的字段表达式
        String[] testExpressions = {
            "d.doc_no AS docname",
            "p.title AS page_name", 
            "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink",
            "p.ref_url",  // 没有AS别名
            "COUNT(*) AS total_count"
        };
        
        for (String expression : testExpressions) {
            System.out.println("\n原始表达式: " + expression);
            
            // 检查是否有AS别名
            boolean hasAlias = expression.toLowerCase().contains(" as ");
            System.out.println("是否有AS别名: " + hasAlias);
            
            if (hasAlias) {
                String[] parts = expression.split("(?i) as ");
                String originalField = parts[0].trim();
                String alias = parts[1].trim().replaceAll("['\"`]", "");
                
                System.out.println("原始字段表达式: " + originalField);
                System.out.println("AS别名: " + alias);
                System.out.println("应该使用的label: " + alias);
            } else {
                System.out.println("没有AS别名，label应该使用字段名本身");
            }
        }
        
        System.out.println("\n=== 演示结束 ===");
    }
    
    @Test
    void testComplexExpressionLabelLogic() {
        // 测试复杂表达式的label逻辑
        String complexExpression = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
        
        // 模拟extractFieldNamesFromExpression的结果
        List<String> extractedFields = Arrays.asList("ref_url", "doc_no", "page_no");
        
        // 检查是否有AS别名
        boolean hasAlias = complexExpression.toLowerCase().contains(" as ");
        assertTrue(hasAlias, "复杂表达式应该有AS别名");
        
        if (hasAlias) {
            String[] parts = complexExpression.split("(?i) as ");
            String alias = parts[1].trim().replaceAll("['\"`]", "");
            assertEquals("doclink", alias);
            
            // 对于有AS别名的复杂表达式，每个提取出的字段都应该使用这个别名作为label
            for (String fieldName : extractedFields) {
                System.out.println("字段名: " + fieldName + ", Label: " + alias);
                // 在实际代码中，每个QueryFieldDTO的label都会是"doclink"
            }
        }
    }
}
