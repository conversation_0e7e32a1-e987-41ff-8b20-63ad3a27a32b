package com.datascope.app.util;

import com.datascope.app.dto.query.QueryFieldDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SQLAnalyzerAliasTest {

    @Mock
    private TableMapper tableMapper;

    @Mock
    private ColumnMapper columnMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private SQLAnalyzer sqlAnalyzer;

    private Datasource datasource;

    @BeforeEach
    void setUp() {
        datasource = new Datasource();
        datasource.setId("test-datasource-id");
    }

    @Test
    void testSimpleFieldAliasHandling() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            // 测试简单字段的别名处理
            String testField = "d.doc_no AS docname";
            
            System.out.println("测试字段: " + testField);
            
            // 模拟字段别名解析逻辑
            String originalField = testField;
            String fieldLabel = testField;
            
            // 处理字段别名
            if (testField.toLowerCase().contains(" as ")) {
                String[] parts = testField.split("(?i) as ");
                originalField = parts[0].trim();
                if (parts.length > 1) {
                    fieldLabel = parts[1].trim().replaceAll("['\"`]", "");
                }
            }
            
            System.out.println("原始字段表达式: " + originalField);
            System.out.println("字段别名: " + fieldLabel);
            
            // 处理表名.字段名的形式
            String fieldName = originalField;
            if (fieldName.contains(".")) {
                String[] parts = fieldName.split("\\.");
                if (parts.length == 2) {
                    fieldName = parts[1];
                }
            }
            
            System.out.println("提取的字段名: " + fieldName);
            System.out.println("应该使用的label: " + fieldLabel);
            
            // 验证结果
            assertEquals("d.doc_no", originalField);
            assertEquals("docname", fieldLabel);
            assertEquals("doc_no", fieldName);
            
            System.out.println("✓ 测试通过：字段名=" + fieldName + ", 别名=" + fieldLabel);
            
        } catch (Exception e) {
            fail("Failed to test simple field alias handling: " + e.getMessage());
        }
    }

    @Test
    void testComplexFieldAliasHandling() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            // 测试复杂表达式的别名处理
            String testField = "COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no)) AS doclink";
            
            System.out.println("\n测试复杂字段: " + testField);
            
            // 模拟字段别名解析逻辑
            String originalField = testField;
            String fieldLabel = testField;
            
            // 处理字段别名
            if (testField.toLowerCase().contains(" as ")) {
                String[] parts = testField.split("(?i) as ");
                originalField = parts[0].trim();
                if (parts.length > 1) {
                    fieldLabel = parts[1].trim().replaceAll("['\"`]", "");
                }
            }
            
            System.out.println("原始字段表达式: " + originalField);
            System.out.println("字段别名: " + fieldLabel);
            
            // 验证结果
            assertEquals("COALESCE(p.ref_url, CONCAT('/docs/', d.doc_no, '/', p.page_no))", originalField);
            assertEquals("doclink", fieldLabel);
            
            System.out.println("✓ 测试通过：复杂表达式别名=" + fieldLabel);
            
        } catch (Exception e) {
            fail("Failed to test complex field alias handling: " + e.getMessage());
        }
    }

    @Test
    void testFieldWithoutAlias() {
        SQLAnalyzer analyzer = new SQLAnalyzer(tableMapper, columnMapper, objectMapper);
        
        try {
            // 测试没有别名的字段
            String testField = "p.ref_url";
            
            System.out.println("\n测试无别名字段: " + testField);
            
            // 模拟字段别名解析逻辑
            String originalField = testField;
            String fieldLabel = testField;
            
            // 处理字段别名
            if (testField.toLowerCase().contains(" as ")) {
                String[] parts = testField.split("(?i) as ");
                originalField = parts[0].trim();
                if (parts.length > 1) {
                    fieldLabel = parts[1].trim().replaceAll("['\"`]", "");
                }
            }
            
            System.out.println("原始字段表达式: " + originalField);
            System.out.println("字段别名: " + fieldLabel);
            
            // 处理表名.字段名的形式
            String fieldName = originalField;
            if (fieldName.contains(".")) {
                String[] parts = fieldName.split("\\.");
                if (parts.length == 2) {
                    fieldName = parts[1];
                }
            }
            
            System.out.println("提取的字段名: " + fieldName);
            System.out.println("应该使用的label: " + fieldName + " (因为没有AS别名)");
            
            // 验证结果
            assertEquals("p.ref_url", originalField);
            assertEquals("p.ref_url", fieldLabel);
            assertEquals("ref_url", fieldName);
            
            System.out.println("✓ 测试通过：无别名字段名=" + fieldName);
            
        } catch (Exception e) {
            fail("Failed to test field without alias: " + e.getMessage());
        }
    }

    @Test
    void testActualExtractFieldsMethod() {
        // 这个测试会调用实际的extractFields方法，但由于没有真实的数据库连接，
        // 会在查询数据库时失败，但我们可以通过日志或异常信息看到字段解析的结果

        try {
            String testSql = "SELECT d.doc_no AS docname FROM tbl_doc d";

            System.out.println("\n测试实际的extractFields方法");
            System.out.println("SQL: " + testSql);

            // 由于没有真实的数据库连接，这个调用会失败，但我们可以看到解析过程
            List<QueryFieldDTO> result = sqlAnalyzer.extractFields(testSql, datasource);

            // 如果到达这里，说明方法执行成功了
            System.out.println("提取到的字段数量: " + result.size());
            for (QueryFieldDTO field : result) {
                System.out.println("字段名: " + field.getName() + ", 别名: " + field.getLabel());
            }

        } catch (Exception e) {
            System.out.println("预期的异常（因为没有真实数据库连接）: " + e.getMessage());
            // 这是预期的，因为没有真实的数据库连接
        }
    }
}
