package com.datascope.app.controller;

import com.datascope.app.dto.datasource.CreateDataSourceRequest;
import com.datascope.app.dto.datasource.DataSourceDTO;
import com.datascope.app.dto.datasource.TestConnectionRequest;
import com.datascope.app.dto.datasource.TestConnectionResultDTO;
import com.datascope.app.service.DatasourceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 数据源控制器测试类
 */
public class DatasourceControllerTest {

    private MockMvc mockMvc;

    @Mock
    private DatasourceService datasourceService;

    @InjectMocks
    private DatasourceController datasourceController;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(datasourceController).build();
    }

    @Test
    @DisplayName("测试MySQL数据源连接")
    public void testMySQLConnection() throws Exception {
        // 准备测试数据
        TestConnectionResultDTO resultDTO = new TestConnectionResultDTO();
        resultDTO.setSuccess(false);
        resultDTO.setMessage("数据库连接失败: Access denied for user 'test_user'@'localhost' (using password: YES)");

        // 模拟服务调用
        when(datasourceService.testNewConnection(any(TestConnectionRequest.class))).thenReturn(resultDTO);

        // 创建请求体
        TestConnectionRequest request = new TestConnectionRequest();
        request.setType("mysql");
        request.setHost("localhost");
        request.setPort(3306);
        request.setDatabaseName("test_db");
        request.setUsername("test_user");
        request.setPassword("test_password");

        // 执行请求并验证结果
        mockMvc.perform(post("/api/datasources/test-connection")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(false))
                .andExpect(jsonPath("$.data.message").value("数据库连接失败: Access denied for user 'test_user'@'localhost' (using password: YES)"));
    }

    @Test
    @DisplayName("测试DB2数据源连接")
    public void testDB2Connection() throws Exception {
        // 准备测试数据
        TestConnectionResultDTO resultDTO = new TestConnectionResultDTO();
        resultDTO.setSuccess(false);
        resultDTO.setMessage("数据库连接失败: [jcc][t4][2043][11550][4.32.28] Exception java.net.ConnectException: Error opening socket to server localhost/127.0.0.1 on port 50,000 with message: Connection refused. ERRORCODE=-4499, SQLSTATE=08001");

        // 模拟服务调用
        when(datasourceService.testNewConnection(any(TestConnectionRequest.class))).thenReturn(resultDTO);

        // 创建请求体
        TestConnectionRequest request = new TestConnectionRequest();
        request.setType("db2");
        request.setHost("localhost");
        request.setPort(50000);
        request.setDatabaseName("test_db");
        request.setUsername("db2inst1");
        request.setPassword("test");

        // 执行请求并验证结果
        mockMvc.perform(post("/api/datasources/test-connection")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(false))
                .andExpect(jsonPath("$.data.message").value("数据库连接失败: [jcc][t4][2043][11550][4.32.28] Exception java.net.ConnectException: Error opening socket to server localhost/127.0.0.1 on port 50,000 with message: Connection refused. ERRORCODE=-4499, SQLSTATE=08001"));
    }

    @Test
    @DisplayName("测试创建MySQL数据源")
    public void testCreateMySQLDatasource() throws Exception {
        // 准备测试数据
        DataSourceDTO dataSourceDTO = new DataSourceDTO();
        dataSourceDTO.setId("test-mysql-id");
        dataSourceDTO.setName("测试MySQL数据源");
        dataSourceDTO.setType("mysql");
        dataSourceDTO.setHost("localhost");
        dataSourceDTO.setPort(3306);
        dataSourceDTO.setDatabase("test_db");
        dataSourceDTO.setUsername("test_user");
        dataSourceDTO.setIsActive(false);

        // 模拟服务调用
        when(datasourceService.createDatasource(any(CreateDataSourceRequest.class))).thenReturn(dataSourceDTO);

        // 创建请求体
        CreateDataSourceRequest request = new CreateDataSourceRequest();
        request.setName("测试MySQL数据源");
        request.setDescription("用于测试的MySQL数据源");
        request.setType("mysql");
        request.setHost("localhost");
        request.setPort(3306);
        request.setDatabaseName("test_db");
        request.setUsername("test_user");
        request.setPassword("test_password");
        request.setSyncFrequency("daily");

        // 执行请求并验证结果
        mockMvc.perform(post("/api/datasources")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value("test-mysql-id"))
                .andExpect(jsonPath("$.data.name").value("测试MySQL数据源"))
                .andExpect(jsonPath("$.data.type").value("mysql"))
                .andExpect(jsonPath("$.data.host").value("localhost"))
                .andExpect(jsonPath("$.data.port").value(3306))
                .andExpect(jsonPath("$.data.database").value("test_db"))
                .andExpect(jsonPath("$.data.username").value("test_user"))
                .andExpect(jsonPath("$.data.isActive").value(false));
    }

    @Test
    @DisplayName("测试创建DB2数据源")
    public void testCreateDB2Datasource() throws Exception {
        // 准备测试数据
        DataSourceDTO dataSourceDTO = new DataSourceDTO();
        dataSourceDTO.setId("test-db2-id");
        dataSourceDTO.setName("测试DB2数据源");
        dataSourceDTO.setType("db2");
        dataSourceDTO.setHost("localhost");
        dataSourceDTO.setPort(50000);
        dataSourceDTO.setDatabase("test_db");
        dataSourceDTO.setUsername("db2inst1");
        dataSourceDTO.setIsActive(false);

        // 模拟服务调用
        when(datasourceService.createDatasource(any(CreateDataSourceRequest.class))).thenReturn(dataSourceDTO);

        // 创建请求体
        CreateDataSourceRequest request = new CreateDataSourceRequest();
        request.setName("测试DB2数据源");
        request.setDescription("用于测试的DB2数据源");
        request.setType("db2");
        request.setHost("localhost");
        request.setPort(50000);
        request.setDatabaseName("test_db");
        request.setUsername("db2inst1");
        request.setPassword("test");
        request.setSyncFrequency("daily");

        // 执行请求并验证结果
        mockMvc.perform(post("/api/datasources")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value("test-db2-id"))
                .andExpect(jsonPath("$.data.name").value("测试DB2数据源"))
                .andExpect(jsonPath("$.data.type").value("db2"))
                .andExpect(jsonPath("$.data.host").value("localhost"))
                .andExpect(jsonPath("$.data.port").value(50000))
                .andExpect(jsonPath("$.data.database").value("test_db"))
                .andExpect(jsonPath("$.data.username").value("db2inst1"))
                .andExpect(jsonPath("$.data.isActive").value(false));
    }
}
