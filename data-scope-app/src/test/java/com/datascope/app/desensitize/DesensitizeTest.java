package com.datascope.app.desensitize;

import com.datascope.app.desensitize.enums.SensitiveDataType;
import com.datascope.app.desensitize.service.DesensitizeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 脱敏功能测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class DesensitizeTest {
    
    @Autowired
    private DesensitizeService desensitizeService;
    
    @Test
    public void testBankCardDesensitize() {
        String bankCard = "6222020200001234567";
        String result = desensitizeService.desensitize(SensitiveDataType.BANK_CARD, bankCard);
        
        log.info("银行卡号脱敏: {} -> {}", bankCard, result);
        
        assertTrue(result.startsWith("622202"));
        assertTrue(result.endsWith("4567"));
        assertTrue(result.contains("***"));
    }
    
    @Test
    public void testCvvDesensitize() {
        String cvv = "123";
        String result = desensitizeService.desensitize(SensitiveDataType.CVV, cvv);
        
        log.info("CVV脱敏: {} -> {}", cvv, result);
        
        assertEquals("***", result);
    }
    
    @Test
    public void testIdCardDesensitize() {
        String idCard = "110101199001011234";
        String result = desensitizeService.desensitize(SensitiveDataType.ID_CARD, idCard);
        
        log.info("身份证脱敏: {} -> {}", idCard, result);
        
        assertTrue(result.startsWith("11"));
        assertTrue(result.endsWith("34"));
        assertTrue(result.contains("*"));
    }
    
    @Test
    public void testOfficerIdDesensitize() {
        String officerId = "P********9";
        String result = desensitizeService.desensitize(SensitiveDataType.OFFICER_ID, officerId);
        
        log.info("军官证号脱敏: {} -> {}", officerId, result);
        
        assertTrue(result.startsWith("P1"));
        assertTrue(result.endsWith("89"));
        assertTrue(result.contains("*"));
    }
    
    @Test
    public void testNameDesensitize() {
        String name = "张三";
        String result = desensitizeService.desensitize(SensitiveDataType.NAME, name);
        
        log.info("姓名脱敏: {} -> {}", name, result);
        
        assertEquals("*三", result);
    }
    
    @Test
    public void testMobileDesensitize() {
        String mobile = "***********";
        String result = desensitizeService.desensitize(SensitiveDataType.MOBILE, mobile);
        
        log.info("手机号脱敏: {} -> {}", mobile, result);
        
        assertTrue(result.startsWith("138"));
        assertTrue(result.endsWith("5678"));
        assertTrue(result.contains("***"));
    }
    
    @Test
    public void testEmailDesensitize() {
        String email = "<EMAIL>";
        String result = desensitizeService.desensitize(SensitiveDataType.EMAIL, email);
        
        log.info("邮箱脱敏: {} -> {}", email, result);
        
        assertTrue(result.startsWith("tes"));
        assertTrue(result.endsWith("@example.com"));
        assertTrue(result.contains("***"));
    }
    
    @Test
    public void testPhoneDesensitize() {
        String phone = "010-********";
        String result = desensitizeService.desensitize(SensitiveDataType.PHONE, phone);
        
        log.info("固定电话脱敏: {} -> {}", phone, result);
        
        assertTrue(result.startsWith("010-"));
        assertTrue(result.endsWith("5678"));
        assertTrue(result.contains("*"));
    }
    
    @Test
    public void testAddressDesensitize() {
        String address = "北京市朝阳区望京SOHO小区A座1001室";
        String result = desensitizeService.desensitize(SensitiveDataType.ADDRESS, address);
        
        log.info("地址脱敏: {} -> {}", address, result);
        
        // 验证数字被替换
        assertFalse(result.contains("1001"));
        assertTrue(result.contains("*"));
    }
    
    @Test
    public void testBatchDesensitize() {
        List<String> bankCards = Arrays.asList(
            "6222020200001234567",
            "6222020200009876543",
            "6222020200001111111"
        );
        
        List<String> results = desensitizeService.batchDesensitize(SensitiveDataType.BANK_CARD, bankCards);
        
        log.info("批量脱敏结果: {}", results);
        
        assertEquals(3, results.size());
        for (String result : results) {
            assertTrue(result.startsWith("622202"));
            assertTrue(result.contains("***"));
        }
    }
    
    @Test
    public void testValidation() {
        assertTrue(desensitizeService.validate(SensitiveDataType.BANK_CARD, "6222020200001234567"));
        assertFalse(desensitizeService.validate(SensitiveDataType.BANK_CARD, "abc123"));
        
        assertTrue(desensitizeService.validate(SensitiveDataType.MOBILE, "***********"));
        assertFalse(desensitizeService.validate(SensitiveDataType.MOBILE, "********"));
        
        assertTrue(desensitizeService.validate(SensitiveDataType.EMAIL, "<EMAIL>"));
        assertFalse(desensitizeService.validate(SensitiveDataType.EMAIL, "invalid_email"));
    }
    
    @Test
    public void testSmartDesensitize() {
        String mobile = "***********";
        String result = desensitizeService.smartDesensitize(mobile);
        
        log.info("智能脱敏: {} -> {}", mobile, result);
        
        assertTrue(result.startsWith("138"));
        assertTrue(result.endsWith("5678"));
        assertTrue(result.contains("***"));
    }
    
    @Test
    public void testGetSupportedTypes() {
        List<SensitiveDataType> types = desensitizeService.getSupportedTypes();
        
        log.info("支持的脱敏类型: {}", types);
        
        assertFalse(types.isEmpty());
        assertTrue(types.contains(SensitiveDataType.BANK_CARD));
        assertTrue(types.contains(SensitiveDataType.MOBILE));
        assertTrue(types.contains(SensitiveDataType.EMAIL));
    }
    
    @Test
    public void testCacheStats() {
        String cacheStats = desensitizeService.getCacheStats();
        
        log.info("缓存统计: {}", cacheStats);
        
        assertNotNull(cacheStats);
    }
} 