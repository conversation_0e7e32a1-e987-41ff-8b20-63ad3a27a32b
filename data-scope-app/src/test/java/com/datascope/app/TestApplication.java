package com.datascope.app;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;

/**
 * 测试应用程序入口
 */
@SpringBootApplication(exclude = {
    SecurityAutoConfiguration.class,
    DataSourceAutoConfiguration.class
})
@ComponentScan(basePackages = {"com.datascope"})
@Profile("test")
public class TestApplication {

    public static void main(String[] args) {
        // 设置测试环境
        System.setProperty("spring.profiles.active", "test");
        SpringApplication application = new SpringApplication(TestApplication.class);
        application.run(args);
    }
}
