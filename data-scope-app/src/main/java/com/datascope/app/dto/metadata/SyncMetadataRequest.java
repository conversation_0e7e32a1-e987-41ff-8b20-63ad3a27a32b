package com.datascope.app.dto.metadata;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 同步元数据请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncMetadataRequest {

    /**
     * 过滤条件
     */
    private FiltersConfig filters;

    /**
     * 过滤配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FiltersConfig {
        /**
         * 包含的模式列表
         */
        private List<String> includeSchemas;

        /**
         * 排除的模式列表
         */
        private List<String> excludeSchemas;

        /**
         * 包含的表列表
         */
        private List<String> includeTables;

        /**
         * 排除的表列表
         */
        private List<String> excludeTables;

        /**
         * 是否加载所有数据库（仅对MySQL有效）
         */
        private Boolean loadAllDatabases;
    }
} 