package com.datascope.app.dto.excel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Excel表头配置
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelHeader {
    
    /**
     * 字段key
     */
    private String key;
    
    /**
     * 表头标题
     */
    private String title;
    
    /**
     * 列宽，默认20
     */
    @Builder.Default
    private Integer width = 20;
    
    /**
     * 数据类型
     */
    private String dataType;
} 