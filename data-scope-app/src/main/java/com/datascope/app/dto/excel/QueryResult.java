package com.datascope.app.dto.excel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 查询结果封装类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryResult {
    
    /**
     * 列信息列表
     */
    private List<ColumnInfo> columns;
    
    /**
     * 数据列表
     */
    private List<Map<String, Object>> data;
} 