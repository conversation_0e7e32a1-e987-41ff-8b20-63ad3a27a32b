package com.datascope.app.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryDTO {

    /**
     * 查询ID
     */
    private String id;

    /**
     * 查询名称
     */
    private String name;

    /**
     * 查询描述
     */
    private String description;

    /**
     * 文件夹ID
     */
    private String folderId;

    /**
     * 状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃，ARCHIVED-已归档
     */
    private String status;

    /**
     * 服务状态：ENABLED-启用，DISABLED-禁用
     */
    private String serviceStatus;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 查询类型：SQL-SQL查询，NATURAL_LANGUAGE-自然语言查询
     */
    private String queryType;

    /**
     * 查询文本
     */
    private String queryText;

    /**
     * 结果数量
     */
    private Integer resultCount;

    /**
     * 执行时间
     */
    private BigDecimal executionTime;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 是否活跃
     */
    private Boolean isActive;

    /**
     * 是否收藏
     */
    private Boolean isFavorite;

    /**
     * 创建人信息
     */
    private UserInfo createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新人信息
     */
    private UserInfo updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 执行次数
     */
    private Integer executionCount;

    /**
     * 最后执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastExecutedAt;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 当前版本
     */
    private QueryVersionDTO currentVersion;

    /**
     * 参数列表
     */
    private List<QueryParameterDTO> parameters;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 用户信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        /**
         * 用户ID
         */
        private String id;

        /**
         * 用户名称
         */
        private String name;

        /**
         * 用户邮箱
         */
        private String email;

        /**
         * 用户头像
         */
        private String avatar;
    }
}
