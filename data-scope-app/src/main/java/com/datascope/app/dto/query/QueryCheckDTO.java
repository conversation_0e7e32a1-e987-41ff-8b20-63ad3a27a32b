package com.datascope.app.dto.query;

import com.datascope.app.dto.integration.IntegrationDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Query;
import com.datascope.app.entity.QueryVersion;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 查询检查结果
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class QueryCheckDTO implements Serializable {

    private Datasource datasource;

    private Query query;

    private QueryVersion queryVersion;

    private IntegrationDTO integrationDTO;

    private Map<String, Object> executionParams;

    private String sql;

    private Set<QueryFieldDTO> queryFieldDTOList;

    private Set<String> encryptedColumns;

    private List<QueryFieldDTO> fields;

}

