package com.datascope.app.dto.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 列元数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColumnDTO {

    /**
     * 列ID
     */
    private String id;

    /**
     * 列名称
     */
    private String name;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 列类型
     */
    private String columnType;

    /**
     * 位置
     */
    private Integer position;

    /**
     * 是否可为空
     */
    private Boolean isNullable;

    /**
     * 是否主键
     */
    private Boolean isPrimaryKey;

    /**
     * 是否唯一
     */
    private Boolean isUnique;

    /**
     * 是否索引
     */
    private Boolean isIndexed;

    /**
     * 是否加密列
     */
    private Boolean isEncrypted;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 字符长度
     */
    private Integer characterLength;

    /**
     * 数值精度
     */
    private Integer numericPrecision;

    /**
     * 数值刻度
     */
    private Integer numericScale;

    /**
     * 列描述
     */
    private String description;

    /**
     * 加密信息
     */
    private Object entryConfig;

    /**
     * 是否需要单独申请授权
     */
    private Boolean isAuthRequired;

    /**
     * 脱敏
     */
    private Boolean authDesensitize;

    /**
     * 脱敏格式类型
     */
    private String formatType;
}
