package com.datascope.app.dto.datasource;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 更新数据源请求
 */
@Data
@Accessors(chain = true)
public class UpdateDataSourceRequest {

    /**
     * 数据源ID
     */
    @NotBlank(message = "数据源ID不能为空")
    private String id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源描述
     */
    private String description;

    /**
     * 数据源类型：mysql, postgresql, oracle, sqlserver, mongodb, elasticsearch
     */
    private String type;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 数据库名称（兼容）
     */
    private String database;

    /**
     * Schema名称
     */
    private String schema;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 同步频率: manual, hourly, daily, weekly, monthly
     */
    private String syncFrequency;

    /**
     * 连接参数
     */
    private String connectionParams;

    /**
     * 加密类型: none, ssl, tls
     */
    private String encryptionType;

    /**
     * 加密选项
     */
    private String encryptionOptions;
}
