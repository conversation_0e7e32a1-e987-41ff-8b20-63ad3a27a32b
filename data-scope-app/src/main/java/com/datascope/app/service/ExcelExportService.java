package com.datascope.app.service;

import com.datascope.app.dto.query.ExecuteQueryParams;
import com.datascope.app.util.ExcelExportUtils;
import com.datascope.app.dto.excel.ExcelHeader;
import com.datascope.app.dto.excel.QueryResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Excel导出服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ExcelExportService {

    /**
     * 动态表头导出
     *
     * @param response   HTTP响应
     * @param fileName   文件名（不含扩展名）
     * @param headers    表头配置
     * @param dataList   数据列表
     */
    void exportWithDynamicHeaders(HttpServletResponse response,
                                 String fileName,
                                 List<ExcelHeader> headers,
                                 List<Map<String, Object>> dataList);

    /**
     * 基于SQL查询结果的动态导出
     *
     * @param response     HTTP响应
     * @param fileName     文件名
     * @param queryResult  查询结果（包含表头和数据）
     */
    void exportFromQueryResult(HttpServletResponse response,
                              String fileName,
                              QueryResult queryResult);

    /**
     * 基于查询结果动态导出
     *
     * @param response response
     * @param queryId queryId
     * @param versionId versionId
     * @param integrationId integrationId
     * @param params params
     */
    void exportQueryData(HttpServletResponse response, String queryId, String versionId,
                         String integrationId, ExecuteQueryParams params);
}
