package com.datascope.app.service.impl;

import com.datascope.app.dto.excel.ExcelHeaderDataDTO;
import com.datascope.app.dto.integration.IntegrationDTO;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.dto.query.ExecuteQueryParams;
import com.datascope.app.dto.query.QueryCheckDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Query;
import com.datascope.app.entity.QueryVersion;
import com.datascope.app.mapper.QueryMapper;
import com.datascope.app.model.PaginationResult;
import com.datascope.app.service.AuthService;
import com.datascope.app.service.ExcelExportService;
import com.datascope.app.service.QueryService;
import com.datascope.app.util.AuthUtils;
import com.datascope.app.util.ExcelExportUtils;
import com.datascope.app.util.JdbcUrlBuilder;
import com.datascope.app.dto.excel.ExcelHeader;
import com.datascope.app.dto.excel.QueryResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Excel导出服务实现类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelExportServiceImpl implements ExcelExportService {

    private final QueryService queryService;
    private final AuthService authService;

    @Override
    public void exportWithDynamicHeaders(HttpServletResponse response,
                                        String fileName,
                                        List<ExcelHeader> headers,
                                        List<Map<String, Object>> dataList) {

        validateExportParams(fileName, headers);

        String finalFileName = generateFileName(fileName);
        log.info("开始导出Excel文件: {}, 数据行数: {}", finalFileName,
                CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());

        try {
            ExcelExportUtils.exportWithDynamicHeaders(response, finalFileName, headers, dataList);
            log.info("Excel文件导出成功: {}", finalFileName);
        } catch (Exception e) {
            log.error("Excel文件导出失败: {}, 错误信息: {}", finalFileName, e.getMessage(), e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void exportFromQueryResult(HttpServletResponse response, String fileName, QueryResult queryResult) {

        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getColumns())) {
            throw new IllegalArgumentException("表头不能为空");
        }

        StopWatch stopWatch = new StopWatch("exportFromQueryResult");
        stopWatch.start();
        String finalFileName = generateFileName(fileName);
        log.info("基于查询结果导出Excel文件: {}, 列数: {}, 数据行数: {}",
                finalFileName,
                queryResult.getColumns().size(),
                CollectionUtils.isEmpty(queryResult.getData()) ? 0 : queryResult.getData().size());

        try {
            ExcelExportUtils.exportFromQueryResult(response, finalFileName, queryResult);
            stopWatch.stop();
            log.info("exportFromQueryResult Excel文件导出成功: {}, 导出耗时: {}ms", finalFileName, stopWatch.getLastTaskTimeMillis());
        } catch (Exception e) {
            log.error("Excel文件导出失败: {}, 错误信息: {}", finalFileName, e.getMessage(), e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void exportQueryData(HttpServletResponse response, String queryId, String versionId,
                                String integrationId, ExecuteQueryParams params) {
        StopWatch stopWatch = new StopWatch("exportQueryData");
        stopWatch.start();
        ExcelHeaderDataDTO excelHeaderDataDTO = this.queryService.downloadQueryHeaders(queryId, versionId, integrationId, params);
        stopWatch.stop();
        log.info("exportQueryData downloadQueryHeaders cost: {}ms", stopWatch.getLastTaskTimeMillis());
        stopWatch.start();
        this.exportFromQueryResult(response, excelHeaderDataDTO.getFileName(), excelHeaderDataDTO.getQueryResult());
        stopWatch.stop();
        log.info("exportQueryData Excel export success, total cost: {}ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 校验导出参数
     */
    private void validateExportParams(String fileName, List<ExcelHeader> headers) {
        if (StringUtils.isBlank(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        if (CollectionUtils.isEmpty(headers)) {
            throw new IllegalArgumentException("表头配置不能为空");
        }

        // 校验表头配置
        for (ExcelHeader header : headers) {
            if (StringUtils.isBlank(header.getKey()) || StringUtils.isBlank(header.getTitle())) {
                throw new IllegalArgumentException("表头配置不完整，key和title不能为空");
            }
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            fileName = "导出数据";
        }

        // 如果文件名不包含时间戳，则添加时间戳
        if (!fileName.contains("_") || !fileName.matches(".*_\\d{8}_\\d{6}.*")) {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            fileName = fileName + "_" + timestamp;
        }

        return fileName;
    }

}
