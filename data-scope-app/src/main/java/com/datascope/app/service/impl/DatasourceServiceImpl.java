package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.constants.CommonConst;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.datasource.*;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.DatasourceStats;
import com.datascope.app.entity.MetadataSync;
import com.datascope.app.exception.BusinessException;
import com.datascope.app.exception.DataSourceNotFoundException;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.DatasourceStatsMapper;
import com.datascope.app.mapper.MetadataSyncMapper;
import com.datascope.app.model.RoleBO;
import com.datascope.app.service.AuthService;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.service.MetadataService;
import com.datascope.app.util.AuthUtils;
import com.datascope.app.util.DatabaseConnectionTester;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源服务实现类
 */
@Slf4j
@Service
public class DatasourceServiceImpl extends ServiceImpl<DatasourceMapper, Datasource> implements DatasourceService {

    @Autowired
    private DatasourceStatsMapper datasourceStatsMapper;

    @Autowired
    private MetadataSyncMapper metadataSyncMapper;

    @Autowired
    private DatabaseConnectionTester databaseConnectionTester;

    @Autowired
    private AuthService authService;

    @Autowired
    private MetadataService metadataService;

    @Override
    public List<DataSourceDTO> listDatasources(DataSourceQueryParam param) {
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();

        // 条件过滤
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.like(Datasource::getName, param.getName());
        }
        if (StringUtils.isNotBlank(param.getType())) {
            wrapper.eq(Datasource::getType, param.getType());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            wrapper.eq(Datasource::getStatus, param.getStatus());
        }

        if (Objects.nonNull(param.getDataSourceNames())) {
            if (param.getDataSourceNames().isEmpty()) {
               return Collections.emptyList();
            } else {
                wrapper.in(Datasource::getDatabaseName, param.getDataSourceNames());
            }
        }

        // 创建时间倒序
        wrapper.orderByDesc(Datasource::getCreatedAt, Datasource::getId);

        // 分页
        Page<Datasource> page = new Page<>(param.getPage(), param.getSize());
        page = this.page(page, wrapper);

        // 转换为DTO
        return page.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public long countDatasources(DataSourceQueryParam param) {
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();

        // 条件过滤
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.like(Datasource::getName, param.getName());
        }
        if (StringUtils.isNotBlank(param.getType())) {
            wrapper.eq(Datasource::getType, param.getType());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            wrapper.eq(Datasource::getStatus, param.getStatus());
        }

        if (Objects.nonNull(param.getDataSourceNames())) {
            if (param.getDataSourceNames().isEmpty()) {
                return 0L;
            } else {
                wrapper.in(Datasource::getDatabaseName, param.getDataSourceNames());
            }
        }

        return this.count(wrapper);
    }

    @Override
    public DataSourceDTO getDatasourceById(String id) throws DataSourceNotFoundException {
        Datasource datasource = getAndValidateDatasource(id);
        return convertToDTO(datasource);
    }

    /**
     * 获取并验证数据源是否存在
     */
    private Datasource getAndValidateDatasource(String id) throws DataSourceNotFoundException {
        Datasource datasource = this.getById(id);
        if (datasource == null) {
            throw new DataSourceNotFoundException("数据源不存在: " + id);
        }
        return datasource;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSourceDTO createDatasource(CreateDataSourceRequest request) {
        // 创建数据源
        Datasource datasource = new Datasource();
        BeanUtils.copyProperties(request, datasource);
        checkRepatDataSource(datasource, null);

        Boolean permission = this.authService.checkRolePermission(Constant.Resource.DB_CREATE, AuthUtils.getLoginName());
        Assert.isTrue(permission, () -> new BusinessException(CommonConst.errorMessage("data-scope数据源创建", "您没有权限创建数据源")));

        // 设置初始状态及ID
        datasource.setId(UUID.randomUUID().toString().replace("-", ""));
        datasource.setStatus("inactive");
        datasource.setCreatedBy(AuthUtils.getLoginName());
        datasource.setUpdatedBy(datasource.getCreatedBy());
        datasource.setCreatedAt(new Date());
        datasource.setUpdatedAt(new Date());
        datasource.setNonce(0);

        // 保存
        this.save(datasource);

        // 创建统计信息
        DatasourceStats stats = new DatasourceStats();
        stats.setId(UUID.randomUUID().toString().replace("-", ""));
        stats.setDatasourceId(datasource.getId());
        stats.setLastUpdate(new Date());
        datasourceStatsMapper.insert(stats);
        this.handlerCreateRole(datasource);
        this.metadataService.authMetaData(new AuthDTO().setId(datasource.getId())
            .setType(AuthTypeEnum.DATASOURCE.getCode()).setAuthRequired(true));
        return convertToDTO(datasource);
    }

    private void checkRepatDataSource(Datasource datasource, String id) {
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Datasource::getHost, datasource.getHost())
            .eq(Datasource::getPort, datasource.getPort())
            .eq(Datasource::getUsername, datasource.getUsername());
        if (StrUtil.isNotBlank(datasource.getSchema())) {
            wrapper.eq(Datasource::getSchema, datasource.getSchema());
        }
        wrapper.notIn(StrUtil.isNotBlank(id), Datasource::getId, id);
        List<Datasource> datasourceList = this.baseMapper.selectList(wrapper);
        Assert.isTrue(CollUtil.isEmpty(datasourceList), () -> new BusinessException("数据源已存在，请检查主机、端口、用户名和模式是否重复"));

        // 校验数据源名称重复
        LambdaQueryWrapper<Datasource> wrapperName = new LambdaQueryWrapper<>();
        wrapperName.eq(Datasource::getName, datasource.getName());
        wrapperName.notIn(StrUtil.isNotBlank(id), Datasource::getId, id);
        List<Datasource> datasources = this.baseMapper.selectList(wrapperName);
        Assert.isTrue(CollUtil.isEmpty(datasources), () -> new BusinessException("数据源名称: " + datasource.getName() + " 已存在，请使用其他名称"));

    }

    @Override
    public void handlerCreateRole(Datasource datasource) {
        List<Datasource> datasourceList = Lists.newArrayList();
        if (datasource == null) {
            datasourceList = this.baseMapper.selectAllWithoutTypeHandler();
        } else {
            datasourceList.add(datasource);
        }
        if (CollUtil.isNotEmpty(datasourceList)) {
            for (Datasource ds : datasourceList) {
                for (int i = 0; i < 2; ++i) {
                    String roleCode = "db:role:" + ds.getDatabaseName();
                    String roleName = "数据源: " + ds.getDatabaseName();
                    switch (i) {
                        case 0:
                            roleCode = roleCode + ":update";
                            roleName = roleName + "-更新角色";
                            break;
                        case 1:
                            roleCode = roleCode + ":search";
                            roleName = roleName + "-发布查询角色";
                            break;
                        default:
                            break;
                    }
                    RoleBO roleBO = new RoleBO()
                        .setRoleCode(roleCode)
                        .setRoleName(roleName)
                        .setResourceStatus("enabled")
                        .setSystemCode("data-scope");

                    log.info("创建角色: {}", roleBO);
                    this.authService.callAddRole(roleBO);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSourceDTO updateDatasource(UpdateDataSourceRequest request) {
        // 获取数据源
        Datasource datasource = this.getById(request.getId());
        if (datasource == null) {
            return null;
        }

        checkRepatDataSource(datasource, request.getId());

        // 创建一个新的数据源对象用于更新，避免乐观锁问题
        Datasource updateEntity = new Datasource();
        updateEntity.setId(request.getId());

        // 更新属性
        if (StringUtils.isNotBlank(request.getName())) {
            updateEntity.setName(request.getName());
        }
        if (StringUtils.isNotBlank(request.getDescription())) {
            updateEntity.setDescription(request.getDescription());
        }
        if (StringUtils.isNotBlank(request.getType())) {
            updateEntity.setType(request.getType());
        }
        if (StringUtils.isNotBlank(request.getHost())) {
            updateEntity.setHost(request.getHost());
        }
        if (request.getPort() != null) {
            updateEntity.setPort(request.getPort());
        }
        if (StringUtils.isNotBlank(request.getDatabaseName())) {
            updateEntity.setDatabaseName(request.getDatabaseName());
        }
        if (StringUtils.isNotBlank(request.getSchema())) {
            updateEntity.setSchema(request.getSchema());
        }
        if (StringUtils.isNotBlank(request.getUsername())) {
            updateEntity.setUsername(request.getUsername());
        }
        if (StringUtils.isNotBlank(request.getPassword())) {
            updateEntity.setPassword(request.getPassword());
        }
        if (StringUtils.isNotBlank(request.getSyncFrequency())) {
            updateEntity.setSyncFrequency(request.getSyncFrequency());
        }
        if (request.getConnectionParams() != null) {
            updateEntity.setConnectionParams(request.getConnectionParams().toString());
        }
        if (StringUtils.isNotBlank(request.getEncryptionType())) {
            updateEntity.setEncryptionType(request.getEncryptionType());
        }
        if (request.getEncryptionOptions() != null) {
            updateEntity.setEncryptionOptions(request.getEncryptionOptions().toString());
        }

        // 设置更新信息
        updateEntity.setUpdatedBy(AuthUtils.getUsername()); // 实际应从上下文获取
        updateEntity.setUpdatedAt(new Date());

        // 使用LambdaUpdateWrapper避免乐观锁问题
        LambdaUpdateWrapper<Datasource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Datasource::getId, request.getId());

        // 手动更新
        this.baseMapper.update(updateEntity, updateWrapper);

        // 重新获取最新数据
        Datasource updatedDatasource = this.getById(request.getId());

        return convertToDTO(updatedDatasource);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDatasource(String id) {
        // 删除数据源
        boolean result = this.removeById(id);

        if (result) {
            // 删除相关统计信息
            LambdaQueryWrapper<DatasourceStats> statsWrapper = new LambdaQueryWrapper<>();
            statsWrapper.eq(DatasourceStats::getDatasourceId, id);
            datasourceStatsMapper.delete(statsWrapper);

            // 删除相关同步记录
            LambdaQueryWrapper<MetadataSync> syncWrapper = new LambdaQueryWrapper<>();
            syncWrapper.eq(MetadataSync::getDatasourceId, id);
            metadataSyncMapper.delete(syncWrapper);
        }

        return result;
    }

    @Override
    public DataSourceStatusDTO checkDatasourceStatus(String id) throws DataSourceNotFoundException {
        Datasource datasource = getAndValidateDatasource(id);

        // 模拟检查状态
        DataSourceStatusDTO statusDTO = new DataSourceStatusDTO();
        statusDTO.setId(id);
        statusDTO.setStatus(datasource.getStatus());
        statusDTO.setIsActive("active".equals(datasource.getStatus()));
        statusDTO.setLastCheckedAt(new Date());
        statusDTO.setMessage("连接正常");

        // 模拟详情
        Map<String, Object> details = new HashMap<>();
        details.put("responseTime", 45);
        details.put("activeConnections", 3);
        details.put("connectionPoolSize", 10);
        statusDTO.setDetails(details);

        return statusDTO;
    }

    @Override
    public TestConnectionResultDTO testConnection(String id) throws DataSourceNotFoundException {
        Datasource datasource = getAndValidateDatasource(id);
        return databaseConnectionTester.testConnection(datasource);
    }

    @Override
    public TestConnectionResultDTO testNewConnection(TestConnectionRequest request) {
        // 将请求转换为Datasource对象
        Datasource datasource = new Datasource();
        datasource.setType(request.getType());
        datasource.setHost(request.getHost());
        datasource.setPort(request.getPort());
        datasource.setDatabaseName(request.getDatabaseName());
        // 优先使用databaseName，如果为空则使用database
        if (datasource.getDatabaseName() == null && request.getDatabase() != null) {
            datasource.setDatabaseName(request.getDatabase());
        }
        datasource.setSchema(request.getSchema());
        datasource.setUsername(request.getUsername());
        datasource.setPassword(request.getPassword());

        // 使用数据库连接测试工具进行真实连接测试
        return databaseConnectionTester.testConnection(datasource);
    }

    @Override
    public DataSourceStatsDTO getDatasourceStats(String id) throws DataSourceNotFoundException {
        // 验证数据源存在
        getAndValidateDatasource(id);

        // 查询统计信息
        LambdaQueryWrapper<DatasourceStats> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DatasourceStats::getDatasourceId, id);
        DatasourceStats stats = datasourceStatsMapper.selectOne(wrapper);

        if (stats == null) {
            throw new DataSourceNotFoundException("数据源统计信息不存在: " + id);
        }

        // 转换为DTO
        DataSourceStatsDTO statsDTO = new DataSourceStatsDTO();
        BeanUtils.copyProperties(stats, statsDTO);
        statsDTO.setDataSourceId(stats.getDatasourceId());

        // 补充一些属性
        statsDTO.setTotalTables(stats.getTablesCount());
        statsDTO.setTotalViews(stats.getViewsCount());
        statsDTO.setTotalQueries(stats.getQueriesCount());

        return statsDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SyncStatusDTO syncDatasourceMetadata(String id, SyncDataSourceRequest request) throws DataSourceNotFoundException {
        // 获取数据源
        Datasource datasource = getAndValidateDatasource(id);

        // 创建同步记录
        MetadataSync metadataSync = createMetadataSync(id);

        try {
            // 模拟同步过程
            Thread.sleep(1000);

            // 更新同步记录状态为成功
            updateMetadataSyncSuccess(metadataSync);

            // 更新数据源同步时间
            updateDatasourceLastSyncTime(id);

            // 返回同步状态
            return createSyncStatusDTO(metadataSync, datasource);
        } catch (Exception e) {
            log.error("同步数据源元数据失败: {}", e.getMessage(), e);

            // 更新同步记录为失败状态
            updateMetadataSyncFailure(metadataSync, e);

            // 返回同步状态
            return createSyncStatusDTO(metadataSync, datasource);
        }
    }

    /**
     * 创建元数据同步记录
     */
    private MetadataSync createMetadataSync(String datasourceId) {
        MetadataSync metadataSync = new MetadataSync();
        metadataSync.setId(UUID.randomUUID().toString().replace("-", ""));
        metadataSync.setDatasourceId(datasourceId);
        metadataSync.setStartTime(new Date());
        metadataSync.setStatus("running");
        metadataSync.setCreatedBy(AuthUtils.getUsername()); // 实际应从上下文获取
        metadataSync.setCreatedAt(new Date());

        // 保存同步记录
        metadataSyncMapper.insert(metadataSync);
        return metadataSync;
    }

    /**
     * 更新元数据同步记录为成功状态
     */
    private void updateMetadataSyncSuccess(MetadataSync metadataSync) {
        metadataSync.setEndTime(new Date());
        metadataSync.setStatus("completed");
        metadataSync.setTablesCount(10);
        metadataSync.setViewsCount(5);
        metadataSync.setSyncDuration(1000);
        metadataSync.setMessage("同步成功");
        metadataSyncMapper.updateById(metadataSync);
    }

    /**
     * 更新元数据同步记录为失败状态
     */
    private void updateMetadataSyncFailure(MetadataSync metadataSync, Exception e) {
        metadataSync.setEndTime(new Date());
        metadataSync.setStatus("failed");
        metadataSync.setMessage("同步失败: " + e.getMessage());
        metadataSyncMapper.updateById(metadataSync);
    }

    /**
     * 更新数据源最后同步时间
     */
    private void updateDatasourceLastSyncTime(String id) {
        LambdaUpdateWrapper<Datasource> finalUpdateWrapper = new LambdaUpdateWrapper<>();
        finalUpdateWrapper.eq(Datasource::getId, id);
        finalUpdateWrapper.set(Datasource::getLastSyncTime, new Date());
        this.update(finalUpdateWrapper);
    }

    /**
     * 创建同步状态DTO
     */
    private SyncStatusDTO createSyncStatusDTO(MetadataSync metadataSync, Datasource datasource) {
        return new SyncStatusDTO()
            .setSyncId(metadataSync.getId())
            .setDataSourceId(datasource.getId())
            .setDataSourceName(datasource.getName())
            .setStartTime(metadataSync.getStartTime())
            .setEndTime(metadataSync.getEndTime())
            .setTablesCount(metadataSync.getTablesCount())
            .setViewsCount(metadataSync.getViewsCount())
            .setSyncDuration(metadataSync.getSyncDuration())
            .setStatus(metadataSync.getStatus())
            .setMessage(metadataSync.getMessage());
    }

    /**
     * 将实体转换为DTO
     */
    private DataSourceDTO convertToDTO(Datasource datasource) {
        DataSourceDTO dto = new DataSourceDTO();
        BeanUtils.copyProperties(datasource, dto);

        // 兼容字段
        dto.setDatabase(datasource.getDatabaseName());

        // 是否活跃
        dto.setIsActive("active".equals(datasource.getStatus()));

        return dto;
    }

    @Override
    public List<Datasource> getDatasourceByNames(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectList(new QueryWrapper<Datasource>().lambda().in(Datasource::getDatabaseName, names));
    }
}
