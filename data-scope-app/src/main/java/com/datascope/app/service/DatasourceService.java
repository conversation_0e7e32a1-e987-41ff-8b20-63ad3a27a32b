package com.datascope.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datascope.app.dto.datasource.*;
import com.datascope.app.entity.Datasource;
import com.datascope.app.exception.DataSourceNotFoundException;

import java.util.List;

/**
 * 数据源服务接口
 */
public interface DatasourceService extends IService<Datasource> {

    /**
     * 分页查询数据源
     *
     * @param param 查询参数
     * @return 数据源列表
     */
    List<DataSourceDTO> listDatasources(DataSourceQueryParam param);

    /**
     * 获取数据源总数
     *
     * @param param 查询参数
     * @return 总数
     */
    long countDatasources(DataSourceQueryParam param);

    /**
     * 根据ID获取数据源
     *
     * @param id 数据源ID
     * @return 数据源信息
     */
    DataSourceDTO getDatasourceById(String id) throws DataSourceNotFoundException;

    /**
     * 创建数据源
     *
     * @param request 创建请求
     * @return 创建后的数据源
     */
    DataSourceDTO createDatasource(CreateDataSourceRequest request);

    /**
     * 创建数据源角色
     *
     * @param datasource 数据源
     */
    void handlerCreateRole(Datasource datasource);

    /**
     * 更新数据源
     *
     * @param request 更新请求
     * @return 更新后的数据源
     */
    DataSourceDTO updateDatasource(UpdateDataSourceRequest request) throws DataSourceNotFoundException;

    /**
     * 删除数据源
     *
     * @param id 数据源ID
     * @return 是否删除成功
     */
    boolean deleteDatasource(String id) throws DataSourceNotFoundException;

    /**
     * 检查数据源状态
     *
     * @param id 数据源ID
     * @return 数据源状态
     */
    DataSourceStatusDTO checkDatasourceStatus(String id) throws DataSourceNotFoundException;

    /**
     * 测试数据源连接
     *
     * @param id 数据源ID
     * @return 连接测试结果
     */
    TestConnectionResultDTO testConnection(String id) throws DataSourceNotFoundException;

    /**
     * 测试新数据源连接（未保存的数据源）
     *
     * @param request 连接信息
     * @return 连接测试结果
     */
    TestConnectionResultDTO testNewConnection(TestConnectionRequest request);

    /**
     * 获取数据源统计信息
     *
     * @param id 数据源ID
     * @return 统计信息
     */
    DataSourceStatsDTO getDatasourceStats(String id) throws DataSourceNotFoundException;

    /**
     * 同步数据源元数据
     *
     * @param id 数据源ID
     * @param request 同步请求
     * @return 同步状态
     */
    SyncStatusDTO syncDatasourceMetadata(String id, SyncDataSourceRequest request) throws DataSourceNotFoundException;

    /**
     * 根据名称获取数据源
     *
     * @param names 数据源名称
     * @return 数据源信息
     */
    List<Datasource> getDatasourceByNames(List<String> names);
}
