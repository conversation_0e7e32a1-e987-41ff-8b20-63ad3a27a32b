package com.datascope.app.service;

import cn.hutool.core.lang.Pair;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Table;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.model.ResourceBO;
import com.datascope.app.model.RoleBO;
import com.datascope.app.model.RoleResourceBindBO;
import com.datascope.app.model.SqlAuthBO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AuthService {

    /**
     * 添加/删除资源
     *
     * @param resourceBO resourceBO
     * @param path path
     */
    void callAddOrDelResource(ResourceBO resourceBO, String path);

    /**
     * 添加角色
     *
     * @param roleBO roleBO
     */
    void callAddRole(RoleBO roleBO);

    /**
     * 绑定角色和资源
     *
     * @param roleResourceBindBO roleResourceBindBO
     * @param roleCode 角色编码
     */
    void callBindRoleResource(RoleResourceBindBO roleResourceBindBO, String roleCode);

    /**
     * 根据资源编码创建角色并绑定资源（便捷方法）
     *
     * @param resourceCode 资源编码
     */
    void createRoleAndBindResource(String resourceCode);

    /**
     * 获取资源
     *
     * @param path path
     * @param token token
     * @return List
     */
    List<AuthResourceBO> getResources(String path, String token);

    /**
     * checkAuth
     *
     * @param path path
     * @param loginName loginName
     * @param token token
     * @return Pair
     */
    Pair<Boolean, List<String>> checkAuth(String path, String loginName, String token);

    /**
     * checkSqlAuth
     *
     * @param authDTO authDTO
     */
    void checkSqlAuth(AuthDTO authDTO);

    /**
     * getAuthToken
     *
     * @return String
     */
    String getAuthToken();

    /**
     * 检查脱敏权限
     *
     * @param datasource 数据源
     * @param sqlParseMap SQL解析结果
     * @param tables 表列表
     * @param loginName 登录名
     * @param sqlAuth SQL权限
     * @return Map<String, Boolean> 键为表名，值为是否有脱敏权限
     */
    Map<String, Boolean> checkDesensitizeAuth(Datasource datasource,
                                              Map<String, Set<String>> sqlParseMap,
                                              List<Table> tables,
                                              String loginName,
                                              SqlAuthBO sqlAuth);

    /**
     * 获取角色权限
     *
     * @param roleCode roleCode
     * @param loginName loginName
     * @return Boolean
     */
    Boolean checkRolePermission(String roleCode, String loginName);

    /**
     * 获取用户角色
     *
     * @param loginName loginName
     * @return List<String>
     */
    List<String> getUserRoles(String loginName);

    /**
     * 获取数据源名称列表
     *
     * @param loginName loginName
     * @param module module
     * @return List<String>
     */
    List<String> getDataSourceNames(String loginName, String module);
}
