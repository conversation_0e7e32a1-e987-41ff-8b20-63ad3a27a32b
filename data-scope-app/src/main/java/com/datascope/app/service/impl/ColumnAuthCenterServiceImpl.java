package com.datascope.app.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Column;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.model.ResourceBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ColumnAuthCenterServiceImpl extends AbstractAuthCenter {

    @Autowired
    private ColumnMapper columnMapper;

    @Autowired
    private TableMapper tableMapper;

    @Autowired
    private TableAuthCenterServiceImpl tableAuthCenterService;

    @Override
    public String getAuthType() {
        return AuthTypeEnum.COLUMN.getCode();
    }

    public ResourceBO assembleBo(AuthDTO authDTO) {
        Column column = authDTO.getColumn();
        String tableId = column.getTableId();
        LambdaQueryWrapper<Table> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Table::getId, tableId);
        Table table = tableMapper.selectOne(wrapper);
        authDTO.setTable(table);
        ResourceBO resourceBO = tableAuthCenterService.assembleBo(authDTO);
        String code = resourceBO.getResourceCode() + StrPool.COLON + column.getName();
        return new ResourceBO().setResourceCode(code).setResourceName(code);
    }


    @Override
    public ResourceBO addAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public ResourceBO removeAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public Pair<Boolean, Boolean> handle(AuthDTO authDTO) {
        LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
        columnWrapper.eq(Column::getId, authDTO.getId());
        Column column = columnMapper.selectOne(columnWrapper);
        Assert.isTrue(Objects.nonNull(column), () -> new RuntimeException("column not found"));
        if (Objects.nonNull(authDTO.getAuthRequired()) && !authDTO.getAuthRequired().equals(column.getIsAuthRequired())) {
            LambdaUpdateWrapper<Column> wrapperUpdate = new LambdaUpdateWrapper<>();
            wrapperUpdate.eq(Column::getId, authDTO.getId());
            wrapperUpdate.set(Column::getIsAuthRequired, authDTO.getAuthRequired());
            this.columnMapper.update(null, wrapperUpdate);
        }
        authDTO.setColumn(column);
        return Pair.of(column.getIsAuthRequired(), authDTO.getAuthRequired());
    }

    @Override
    public Pair<Boolean, Boolean> desensitizeHandler(AuthDTO authDTO) {
        LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
        columnWrapper.eq(Column::getId, authDTO.getId());
        Column column = columnMapper.selectOne(columnWrapper);
        Assert.isTrue(Objects.nonNull(column), () -> new RuntimeException("column not found"));
        if (Objects.nonNull(authDTO.getAuthDesensitize())) {
            LambdaUpdateWrapper<Column> wrapperUpdate = new LambdaUpdateWrapper<>();
            wrapperUpdate.eq(Column::getId, authDTO.getId());
            if (!authDTO.getAuthDesensitize().equals(column.getAuthDesensitize())) {
                wrapperUpdate.set(Column::getAuthDesensitize, authDTO.getAuthDesensitize());
            }
            wrapperUpdate.set(Column::getFormatType, authDTO.getFormatType());
            this.columnMapper.update(null, wrapperUpdate);
        }
        authDTO.setColumn(column);
        return Pair.of(column.getAuthDesensitize(), authDTO.getAuthDesensitize());
    }
}
