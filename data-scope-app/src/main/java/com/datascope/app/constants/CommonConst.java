package com.datascope.app.constants;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.datascope.app.common.enums.ModuleTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CommonConst {

    public static final String REPEAT_COMMIT_KEY = "repeat:{key}";

    public static String getRepeatCommitKey(String key) {
        Map<String, Object> kvMap = new HashMap<>(1);
        kvMap.put("key", key);
        return StrUtil.format(REPEAT_COMMIT_KEY, kvMap, false);
    }

    public static String errorMessage(String name, String prefixMessage) {
        Map<String, Object> kvMap = new HashMap<>(2);
        kvMap.put("url", "https://mfmc.yeepay.com/fmc-boss/index.html#/permission-get");
        kvMap.put("name", name);
        return StrUtil.format(StrUtil.isBlank(prefixMessage) ? ModuleTypeEnum.OPERATOR_MT
            : prefixMessage + StrPool.COMMA + ModuleTypeEnum.OPERATOR_MT, kvMap, false);
    }

}
