package com.datascope.app.constants;

/**
 * <AUTHOR>
 */
public class Constant {

    public static final String AES = "aes";

    public static final String AES_KEY = "key";

    public static final String GM = "gm";

    public static final String JSON_ENCRYPT = "encrypt_json_key";

    public static final String CODE = "code";

    public static final String DATA = "data";

    public static final String EXPORT = "export";

    public static final String MAX_ROWS = "maxRows";

    public static final String COLUMNS = "columns";

    public static final String FIELD = "field";

    public static final String LABEL = "label";

    public static final String TYPE = "type";

    public static final String ENUM = "enum";

    public static final String QUERY_NOTE_ADMIN = "QUERY_NOTE_ADMIN";

    public static class UrlPath {
        public static final String ADD_RESOURCE = "/meta/add_resource";
        public static final String DEL_RESOURCE = "/meta/delete_resource";
        public static final String ADD_ROLE = "/meta/add_role";
        public static final String QUERY_ROLE_USER = "/meta/get_users_by_role";
        public static final String AUTH_ROLES = "/auth/roles";
    }

    public static class Resource {
        public static final String DB_CREATE = "db:create";
    }
}
