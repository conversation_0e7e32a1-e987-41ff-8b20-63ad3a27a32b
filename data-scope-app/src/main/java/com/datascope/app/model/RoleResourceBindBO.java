package com.datascope.app.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 角色资源绑定信息模型
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RoleResourceBindBO implements Serializable {
    private String systemCode;
    private List<String> resourceCode;
} 