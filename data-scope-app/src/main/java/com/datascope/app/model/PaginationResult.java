package com.datascope.app.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分页处理结果类
 * 封装智能分页处理后的SQL语句和分页信息
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public class PaginationResult {

    /**
     * 处理后的SQL语句（可能包含LIMIT子句）
     */
    private final String sql;

    /**
     * 总记录数（仅在需要分页时计算）
     */
    private final Long total;

    /**
     * 当前页码
     */
    private final int page;

    /**
     * 每页大小
     */
    private final int size;
}
