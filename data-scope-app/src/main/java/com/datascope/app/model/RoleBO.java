package com.datascope.app.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 角色信息模型
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RoleBO implements Serializable {
    private String roleCode;
    private String roleName;
    private String resourceStatus;
    private String systemCode;
} 