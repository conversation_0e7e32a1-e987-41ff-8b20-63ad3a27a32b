package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * 固定电话脱敏处理器
 * 显示区号和后4位
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class PhoneProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 固定电话最小长度
     */
    private static final int MIN_LENGTH = 7;
    
    /**
     * 固定电话最大长度
     */
    private static final int MAX_LENGTH = 15;
    
    /**
     * 后缀保留位数
     */
    private static final int SUFFIX_LENGTH = 4;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.PHONE;
    }
    
    @Override
    protected String doProcess(String originalData) {
        int length = originalData.length();
        
        // 如果长度不够，则全部显示
        if (length <= SUFFIX_LENGTH + 3) {
            return originalData;
        }
        
        // 判断是否包含区号分隔符
        if (originalData.contains("-")) {
            return processWithSeparator(originalData);
        } else {
            return processWithoutSeparator(originalData);
        }
    }
    
    /**
     * 处理包含分隔符的电话号码
     */
    private String processWithSeparator(String originalData) {
        int separatorIndex = originalData.indexOf('-');
        if (separatorIndex == -1) {
            return originalData;
        }
        
        String areaCode = originalData.substring(0, separatorIndex + 1);
        String phoneNumber = originalData.substring(separatorIndex + 1);
        
        if (phoneNumber.length() <= SUFFIX_LENGTH) {
            return originalData;
        }
        
        String suffix = safeSubstring(phoneNumber, phoneNumber.length() - SUFFIX_LENGTH);
        String mask = generateMask(phoneNumber.length() - SUFFIX_LENGTH);
        
        return buildMaskedString(areaCode, mask, suffix);
    }
    
    /**
     * 处理不包含分隔符的电话号码
     */
    private String processWithoutSeparator(String originalData) {
        int length = originalData.length();
        
        // 假设前3-4位是区号
        int areaCodeLength = getAreaCodeLength(originalData);
        
        String areaCode = safeSubstring(originalData, 0, areaCodeLength);
        String phoneNumber = safeSubstring(originalData, areaCodeLength);
        
        if (phoneNumber.length() <= SUFFIX_LENGTH) {
            return originalData;
        }
        
        String suffix = safeSubstring(phoneNumber, phoneNumber.length() - SUFFIX_LENGTH);
        String mask = generateMask(phoneNumber.length() - SUFFIX_LENGTH);
        
        return buildMaskedString(areaCode, mask, suffix);
    }
    
    /**
     * 获取区号长度
     */
    private int getAreaCodeLength(String phoneNumber) {
        int length = phoneNumber.length();
        
        // 根据号码长度判断区号长度
        if (length == 7 || length == 8) {
            return 0; // 没有区号
        } else if (length <= 10) {
            return 3; // 3位区号
        } else {
            return 4; // 4位区号
        }
    }
    
    @Override
    public boolean isValid(String data) {
        if (!isLengthValid(data, MIN_LENGTH, MAX_LENGTH)) {
            return false;
        }
        
        // 验证是否为有效的固定电话格式
        String cleanData = data.replace("-", "").replace(" ", "");
        return isNumeric(cleanData);
    }
} 