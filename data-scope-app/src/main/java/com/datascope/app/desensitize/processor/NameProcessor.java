package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * 姓名脱敏处理器
 * 隐藏第一个字
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class NameProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 姓名最小长度
     */
    private static final int MIN_LENGTH = 2;
    
    /**
     * 姓名最大长度
     */
    private static final int MAX_LENGTH = 20;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.NAME;
    }
    
    @Override
    protected String doProcess(String originalData) {
        int length = originalData.length();
        
        if (length == 1) {
            return DEFAULT_MASK;
        }
        
        // 第一个字符用*替换，其余保持原样
        String mask = DEFAULT_MASK;
        String suffix = safeSubstring(originalData, 1);
        
        return buildMaskedString(mask, "", suffix);
    }
    
    @Override
    public boolean isValid(String data) {
        if (!isLengthValid(data, MIN_LENGTH, MAX_LENGTH)) {
            return false;
        }
        
        // 验证是否包含中文字符、英文字母或常见的名字符号
        return data.chars().allMatch(c -> 
            Character.isLetter(c) || 
            Character.UnicodeScript.of(c) == Character.UnicodeScript.HAN ||
            c == '·' || c == '.' || c == '-' || c == ' '
        );
    }
} 