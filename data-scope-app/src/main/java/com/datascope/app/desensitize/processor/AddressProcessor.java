package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 地址脱敏处理器
 * 隐藏小区信息以及门牌号（隐藏数字）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class AddressProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 地址最小长度
     */
    private static final int MIN_LENGTH = 5;
    
    /**
     * 地址最大长度
     */
    private static final int MAX_LENGTH = 200;
    
    /**
     * 数字匹配模式
     */
    private static final Pattern DIGIT_PATTERN = Pattern.compile("\\d");
    
    /**
     * 小区相关关键词
     */
    private static final String[] COMMUNITY_KEYWORDS = {
        "小区", "花园", "公寓", "大厦", "广场", "中心", "苑", "园", "庭", "城", "村", "湾", "府", "居", "馆"
    };
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.ADDRESS;
    }
    
    @Override
    protected String doProcess(String originalData) {
        String result = originalData;
        
        // 隐藏数字
        result = DIGIT_PATTERN.matcher(result).replaceAll(DEFAULT_MASK);
        
        // 隐藏小区信息
        result = maskCommunityInfo(result);
        
        return result;
    }
    
    /**
     * 掩码小区信息
     */
    private String maskCommunityInfo(String address) {
        String result = address;
        
        // 查找包含小区关键词的部分并进行掩码
        for (String keyword : COMMUNITY_KEYWORDS) {
            int keywordIndex = result.indexOf(keyword);
            if (keywordIndex != -1) {
                // 找到关键词前面的可能是小区名称的部分
                int startIndex = findCommunityNameStart(result, keywordIndex);
                if (startIndex != -1 && startIndex < keywordIndex) {
                    // 保留关键词，但隐藏小区名称
                    String before = result.substring(0, startIndex);
                    String communityName = result.substring(startIndex, keywordIndex);
                    String keywordPart = keyword;
                    String after = result.substring(keywordIndex + keyword.length());
                    
                    // 将小区名称替换为*，保留关键词
                    String maskedCommunity = generateMask(communityName.length());
                    result = before + maskedCommunity + keywordPart + after;
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找小区名称的开始位置
     */
    private int findCommunityNameStart(String address, int keywordIndex) {
        if (keywordIndex <= 0) {
            return -1;
        }
        
        // 向前查找，找到可能的小区名称开始位置
        // 一般在省市区之后，或者在道路名称之后
        String[] locationKeywords = {"省", "市", "区", "县", "路", "街", "道", "巷", "弄"};
        
        int startIndex = 0;
        for (String locKeyword : locationKeywords) {
            int locIndex = address.lastIndexOf(locKeyword, keywordIndex);
            if (locIndex != -1) {
                startIndex = Math.max(startIndex, locIndex + locKeyword.length());
            }
        }
        
        return startIndex;
    }
    
    @Override
    public boolean isValid(String data) {
        if (!isLengthValid(data, MIN_LENGTH, MAX_LENGTH)) {
            return false;
        }
        
        // 地址验证：包含中文字符，可能包含数字和常见标点
        return data.chars().anyMatch(c -> 
            Character.UnicodeScript.of(c) == Character.UnicodeScript.HAN
        );
    }
} 