package com.datascope.app.desensitize.core;

import com.datascope.app.desensitize.enums.SensitiveDataType;

/**
 * 脱敏数据处理器接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SensitiveDataProcessor {
    
    /**
     * 获取支持的脱敏类型
     * 
     * @return 脱敏类型
     */
    SensitiveDataType getSupportedType();
    
    /**
     * 执行脱敏操作
     * 
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    String process(String originalData);
    
    /**
     * 验证数据格式是否正确
     * 
     * @param data 待验证数据
     * @return 是否有效
     */
    boolean isValid(String data);
    
    /**
     * 获取处理器优先级，数字越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 0;
    }
} 