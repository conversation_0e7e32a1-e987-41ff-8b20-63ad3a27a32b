package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * 身份证脱敏处理器
 * 显示前2位+*(实际位数)+后2位
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class IdCardProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 身份证最小长度
     */
    private static final int MIN_LENGTH = 15;
    
    /**
     * 身份证最大长度
     */
    private static final int MAX_LENGTH = 18;
    
    /**
     * 前缀保留位数
     */
    private static final int PREFIX_LENGTH = 2;
    
    /**
     * 后缀保留位数
     */
    private static final int SUFFIX_LENGTH = 2;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.ID_CARD;
    }
    
    @Override
    protected String doProcess(String originalData) {
        int length = originalData.length();
        
        // 如果长度不够，则全部显示
        if (length <= PREFIX_LENGTH + SUFFIX_LENGTH) {
            return originalData;
        }
        
        String prefix = safeSubstring(originalData, 0, PREFIX_LENGTH);
        String suffix = safeSubstring(originalData, length - SUFFIX_LENGTH);
        
        // 中间部分用实际长度的*替换
        int maskLength = length - PREFIX_LENGTH - SUFFIX_LENGTH;
        String mask = generateMask(maskLength);
        
        return buildMaskedString(prefix, mask, suffix);
    }
    
    @Override
    public boolean isValid(String data) {
        if (!isLengthValid(data, MIN_LENGTH, MAX_LENGTH)) {
            return false;
        }
        
        // 身份证号码前17位必须是数字，最后一位可以是数字或X
        if (data.length() == 18) {
            String prefix = data.substring(0, 17);
            String suffix = data.substring(17);
            return isNumeric(prefix) && (isNumeric(suffix) || "X".equalsIgnoreCase(suffix));
        } else {
            return isNumeric(data);
        }
    }
} 