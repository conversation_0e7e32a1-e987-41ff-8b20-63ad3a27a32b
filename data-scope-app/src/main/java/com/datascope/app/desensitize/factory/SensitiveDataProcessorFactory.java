package com.datascope.app.desensitize.factory;

import com.datascope.app.desensitize.core.SensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 脱敏处理器工厂
 * 提供高性能的脱敏处理器管理和缓存
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class SensitiveDataProcessorFactory {

    /**
     * 脱敏处理器映射表
     */
    private final Map<SensitiveDataType, SensitiveDataProcessor> processorMap = new ConcurrentHashMap<>();

    /**
     * 脱敏结果缓存
     */
    private final Cache<String, String> resultCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();

    /**
     * 所有脱敏处理器
     */
    private final List<SensitiveDataProcessor> processors;

    @Autowired
    public SensitiveDataProcessorFactory(List<SensitiveDataProcessor> processors) {
        this.processors = processors;
    }

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initProcessors() {
        processors.stream()
                .sorted((p1, p2) -> Integer.compare(p1.getPriority(), p2.getPriority()))
                .forEach(processor -> {
                    SensitiveDataType type = processor.getSupportedType();
                    SensitiveDataProcessor existing = processorMap.put(type, processor);
                    if (existing != null) {
                        log.warn("重复的脱敏处理器类型: {}, 替换处理器: {} -> {}",
                                type, existing.getClass().getSimpleName(), processor.getClass().getSimpleName());
                    }
                    log.info("注册脱敏处理器: {} -> {}", type, processor.getClass().getSimpleName());
                });

        log.info("脱敏处理器工厂初始化完成，共注册 {} 个处理器", processorMap.size());
    }

    /**
     * 获取指定类型的脱敏处理器
     *
     * @param type 脱敏类型
     * @return 脱敏处理器，如果不存在返回null
     */
    public SensitiveDataProcessor getProcessor(SensitiveDataType type) {
        return processorMap.get(type);
    }

    /**
     * 执行脱敏处理
     *
     * @param type 脱敏类型
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    public String process(SensitiveDataType type, String originalData) {
        if (type == null) {
            return "******";
        }
        if (originalData == null || originalData.trim().isEmpty()) {
            return originalData;
        }

        // 尝试从缓存获取
        String cacheKey = type.getCode() + ":" + originalData;
        String cachedResult = resultCache.getIfPresent(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 获取处理器
        SensitiveDataProcessor processor = getProcessor(type);
        if (processor == null) {
            log.warn("未找到脱敏处理器: {}", type);
            return originalData;
        }

        // 执行脱敏
        String result = processor.process(originalData);

        // 缓存结果
        resultCache.put(cacheKey, result);

        return result;
    }

    /**
     * 执行脱敏处理（通过类型码）
     *
     * @param typeCode 脱敏类型码
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    public String process(String typeCode, String originalData) {
        SensitiveDataType type = SensitiveDataType.fromCode(typeCode);
        return process(type, originalData);
    }

    /**
     * 批量脱敏处理
     *
     * @param type 脱敏类型
     * @param originalDataList 原始数据列表
     * @return 脱敏后的数据列表
     */
    public List<String> batchProcess(SensitiveDataType type, List<String> originalDataList) {
        if (originalDataList == null || originalDataList.isEmpty()) {
            return originalDataList;
        }

        return originalDataList.parallelStream()
                .map(data -> process(type, data))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 验证数据格式
     *
     * @param type 脱敏类型
     * @param data 待验证数据
     * @return 是否有效
     */
    public boolean isValid(SensitiveDataType type, String data) {
        if (type == null) {
            return false;
        }

        SensitiveDataProcessor processor = getProcessor(type);
        if (processor == null) {
            return false;
        }

        return processor.isValid(data);
    }

    /**
     * 获取所有支持的脱敏类型
     *
     * @return 脱敏类型列表
     */
    public List<SensitiveDataType> getSupportedTypes() {
        return processorMap.keySet().stream()
                .sorted((t1, t2) -> t1.getCode().compareTo(t2.getCode()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        resultCache.invalidateAll();
        log.info("脱敏结果缓存已清空");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return resultCache.stats().toString();
    }
}
