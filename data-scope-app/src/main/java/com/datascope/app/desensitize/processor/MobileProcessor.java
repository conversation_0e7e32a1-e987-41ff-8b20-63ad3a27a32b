package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * 手机号脱敏处理器
 * 显示前3位+***+后4位
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class MobileProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 手机号标准长度
     */
    private static final int STANDARD_LENGTH = 11;
    
    /**
     * 前缀保留位数
     */
    private static final int PREFIX_LENGTH = 3;
    
    /**
     * 后缀保留位数
     */
    private static final int SUFFIX_LENGTH = 4;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.MOBILE;
    }
    
    @Override
    protected String doProcess(String originalData) {
        int length = originalData.length();
        
        // 如果长度不够，则全部显示
        if (length <= PREFIX_LENGTH + SUFFIX_LENGTH) {
            return originalData;
        }
        
        String prefix = safeSubstring(originalData, 0, PREFIX_LENGTH);
        String suffix = safeSubstring(originalData, length - SUFFIX_LENGTH);
        
        return buildMaskedString(prefix, DEFAULT_MASK_SEQUENCE, suffix);
    }
    
    @Override
    public boolean isValid(String data) {
        if (data.length() != STANDARD_LENGTH) {
            return false;
        }
        
        if (!isNumeric(data)) {
            return false;
        }
        
        // 验证是否为有效的手机号码格式（以1开头，第二位为3-9）
        return data.startsWith("1") && 
               data.charAt(1) >= '3' && data.charAt(1) <= '9';
    }
} 