package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 邮箱脱敏处理器
 * @前面显示3位，后面显示3个*，@后面完整显示
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class EmailProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * 邮箱最小长度
     */
    private static final int MIN_LENGTH = 5;
    
    /**
     * 邮箱最大长度
     */
    private static final int MAX_LENGTH = 50;
    
    /**
     * 用户名部分显示位数
     */
    private static final int USERNAME_SHOW_LENGTH = 3;
    
    /**
     * 掩码长度
     */
    private static final int MASK_LENGTH = 3;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.EMAIL;
    }
    
    @Override
    protected String doProcess(String originalData) {
        int atIndex = originalData.indexOf('@');
        if (atIndex == -1) {
            return originalData;
        }
        
        String username = originalData.substring(0, atIndex);
        String domain = originalData.substring(atIndex);
        
        // 如果用户名长度不够，则全部显示
        if (username.length() <= USERNAME_SHOW_LENGTH) {
            return originalData;
        }
        
        // 显示前3位，后面显示3个*
        String prefix = safeSubstring(username, 0, USERNAME_SHOW_LENGTH);
        String mask = StringUtils.repeat(DEFAULT_MASK, MASK_LENGTH);
        
        return buildMaskedString(prefix, mask, domain);
    }
    
    @Override
    public boolean isValid(String data) {
        if (!isLengthValid(data, MIN_LENGTH, MAX_LENGTH)) {
            return false;
        }
        
        // 简单的邮箱格式验证
        return data.contains("@") && 
               data.indexOf('@') > 0 && 
               data.indexOf('@') < data.length() - 1 &&
               data.lastIndexOf('@') == data.indexOf('@') &&
               data.substring(data.indexOf('@') + 1).contains(".");
    }
} 