package com.datascope.app.desensitize.enums;

import lombok.Getter;

/**
 * 脱敏数据类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum SensitiveDataType {
    /**
     * 银行卡号 - 显示前6位+***+后4位
     */
    BANK_CARD("BANK_CARD", "银行卡号"),

    /**
     * 有效期CVV - 完全屏蔽
     */
    CVV("CVV", "有效期CVV"),

    /**
     * 身份证 - 显示前2位+*(实际位数)+后2位
     */
    ID_CARD("ID_CARD", "身份证"),

    /**
     * 军官证号/护照号 - 显示前2位+*(实际位数)+后2位
     */
    OFFICER_ID("OFFICER_ID", "军官证号/护照号"),

    /**
     * 姓名 - 隐藏第一个字
     */
    NAME("NAME", "姓名"),

    /**
     * 固定电话 - 显示区号和后4位
     */
    PHONE("PHONE", "固定电话"),

    /**
     * 手机号 - 显示前3位+***+后4位
     */
    MOBILE("MOBILE", "手机号"),

    /**
     * 邮箱 - @前面显示3位，后面显示3个*，@后面完整显示
     */
    EMAIL("EMAIL", "邮箱"),

    /**
     * 地址 - 隐藏小区信息以及门牌号（隐藏数字）
     */
    ADDRESS("ADDRESS", "地址");

    private final String code;
    private final String description;

    SensitiveDataType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取脱敏类型
     *
     * @param code 类型码
     * @return 脱敏类型，如果不存在则返回null
     */
    public static SensitiveDataType fromCode(String code) {
        for (SensitiveDataType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
