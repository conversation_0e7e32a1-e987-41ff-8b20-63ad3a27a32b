package com.datascope.app.desensitize.service;

import com.datascope.app.desensitize.enums.SensitiveDataType;
import com.datascope.app.desensitize.factory.SensitiveDataProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 脱敏数据服务
 * 提供统一的脱敏服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesensitizeService {
    
    private final SensitiveDataProcessorFactory processorFactory;
    
    /**
     * 脱敏单个数据
     * 
     * @param type 脱敏类型
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    public String desensitize(SensitiveDataType type, String originalData) {
        try {
            return processorFactory.process(type, originalData);
        } catch (Exception e) {
            log.error("脱敏处理失败 - 类型: {}, 数据: {}", type, originalData, e);
            return originalData;
        }
    }
    
    /**
     * 脱敏单个数据（通过类型码）
     * 
     * @param typeCode 脱敏类型码
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    public String desensitize(String typeCode, String originalData) {
        try {
            return processorFactory.process(typeCode, originalData);
        } catch (Exception e) {
            log.error("脱敏处理失败 - 类型码: {}, 数据: {}", typeCode, originalData, e);
            return originalData;
        }
    }
    
    /**
     * 批量脱敏
     * 
     * @param type 脱敏类型
     * @param originalDataList 原始数据列表
     * @return 脱敏后的数据列表
     */
    public List<String> batchDesensitize(SensitiveDataType type, List<String> originalDataList) {
        try {
            return processorFactory.batchProcess(type, originalDataList);
        } catch (Exception e) {
            log.error("批量脱敏处理失败 - 类型: {}, 数据量: {}", type, originalDataList.size(), e);
            return originalDataList;
        }
    }
    
    /**
     * 验证数据格式
     * 
     * @param type 脱敏类型
     * @param data 待验证数据
     * @return 验证结果
     */
    public boolean validate(SensitiveDataType type, String data) {
        try {
            return processorFactory.isValid(type, data);
        } catch (Exception e) {
            log.error("数据验证失败 - 类型: {}, 数据: {}", type, data, e);
            return false;
        }
    }
    
    /**
     * 获取所有支持的脱敏类型
     * 
     * @return 脱敏类型列表
     */
    public List<SensitiveDataType> getSupportedTypes() {
        return processorFactory.getSupportedTypes();
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        processorFactory.clearCache();
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return processorFactory.getCacheStats();
    }
    
    /**
     * 智能脱敏 - 根据数据内容自动识别类型并脱敏
     * 
     * @param data 原始数据
     * @return 脱敏后的数据
     */
    public String smartDesensitize(String data) {
        if (data == null || data.trim().isEmpty()) {
            return data;
        }
        
        // 尝试各种类型进行验证和脱敏
        for (SensitiveDataType type : getSupportedTypes()) {
            if (validate(type, data)) {
                log.debug("智能脱敏识别类型: {} -> {}", type, data);
                return desensitize(type, data);
            }
        }
        
        // 如果没有匹配的类型，返回原始数据
        log.debug("智能脱敏未识别类型: {}", data);
        return data;
    }
} 