package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * 银行卡号脱敏处理器
 * 显示前6位+***+后4位
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class BankCardProcessor extends AbstractSensitiveDataProcessor {

    /**
     * 银行卡号最小长度
     */
    private static final int MIN_LENGTH = 10;

    /**
     * 银行卡号最大长度
     */
    private static final int MAX_LENGTH = 19;

    /**
     * 前缀保留位数
     */
    private static final int PREFIX_LENGTH = 6;

    /**
     * 后缀保留位数
     */
    private static final int SUFFIX_LENGTH = 4;

    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.BANK_CARD;
    }

    @Override
    protected String doProcess(String originalData) {
        int length = originalData.length();

        // 如果长度不够，则全部显示
        if (length <= PREFIX_LENGTH + SUFFIX_LENGTH) {
            return originalData;
        }

        String prefix = safeSubstring(originalData, 0, PREFIX_LENGTH);
        String suffix = safeSubstring(originalData, length - SUFFIX_LENGTH);

        return buildMaskedString(prefix, DEFAULT_MASK_SEQUENCE, suffix);
    }

    @Override
    public boolean isValid(String data) {
        // isLengthValid(data, MIN_LENGTH, MAX_LENGTH) &&
        return isNumeric(data);
    }
}
