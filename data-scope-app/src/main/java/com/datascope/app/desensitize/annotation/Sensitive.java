package com.datascope.app.desensitize.annotation;

import com.datascope.app.desensitize.enums.SensitiveDataType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 脱敏注解
 * 用于标记需要脱敏的字段
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Sensitive {
    
    /**
     * 脱敏类型
     * 
     * @return 脱敏类型
     */
    SensitiveDataType type();
    
    /**
     * 是否启用脱敏
     * 
     * @return 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 描述信息
     * 
     * @return 描述信息
     */
    String description() default "";
} 