package com.datascope.app.desensitize.core;

import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.apache.commons.lang3.StringUtils;

/**
 * 脱敏数据处理器抽象基类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class AbstractSensitiveDataProcessor implements SensitiveDataProcessor {
    
    /**
     * 默认掩码字符
     */
    protected static final String DEFAULT_MASK = "*";
    
    /**
     * 默认掩码序列
     */
    protected static final String DEFAULT_MASK_SEQUENCE = "***";
    
    @Override
    public final String process(String originalData) {
        // 空值处理
        if (StringUtils.isBlank(originalData)) {
            return originalData;
        }
        
        // 格式验证
        if (!isValid(originalData)) {
            return originalData;
        }
        
        // 执行具体的脱敏逻辑
        return doProcess(originalData);
    }
    
    /**
     * 执行具体的脱敏逻辑
     * 
     * @param originalData 原始数据
     * @return 脱敏后的数据
     */
    protected abstract String doProcess(String originalData);
    
    /**
     * 生成指定长度的掩码字符串
     * 
     * @param length 长度
     * @return 掩码字符串
     */
    protected String generateMask(int length) {
        if (length <= 0) {
            return "";
        }
        return StringUtils.repeat(DEFAULT_MASK, length);
    }
    
    /**
     * 获取字符串的安全子字符串
     * 
     * @param str 原字符串
     * @param start 开始位置
     * @param end 结束位置
     * @return 子字符串
     */
    protected String safeSubstring(String str, int start, int end) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        
        int length = str.length();
        start = Math.max(0, start);
        end = Math.min(length, end);
        
        if (start >= end) {
            return "";
        }
        
        return str.substring(start, end);
    }
    
    /**
     * 获取字符串的安全子字符串（从开始位置到结尾）
     * 
     * @param str 原字符串
     * @param start 开始位置
     * @return 子字符串
     */
    protected String safeSubstring(String str, int start) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        
        return safeSubstring(str, start, str.length());
    }
    
    /**
     * 构建脱敏后的字符串
     * 
     * @param prefix 前缀
     * @param mask 掩码
     * @param suffix 后缀
     * @return 脱敏后的字符串
     */
    protected String buildMaskedString(String prefix, String mask, String suffix) {
        StringBuilder sb = new StringBuilder();
        
        if (StringUtils.isNotBlank(prefix)) {
            sb.append(prefix);
        }
        
        if (StringUtils.isNotBlank(mask)) {
            sb.append(mask);
        }
        
        if (StringUtils.isNotBlank(suffix)) {
            sb.append(suffix);
        }
        
        return sb.toString();
    }
    
    /**
     * 验证字符串是否为数字
     * 
     * @param str 待验证字符串
     * @return 是否为数字
     */
    protected boolean isNumeric(String str) {
        return StringUtils.isNumeric(str);
    }
    
    /**
     * 验证字符串长度是否在指定范围内
     * 
     * @param str 待验证字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否在范围内
     */
    protected boolean isLengthValid(String str, int minLength, int maxLength) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }
} 