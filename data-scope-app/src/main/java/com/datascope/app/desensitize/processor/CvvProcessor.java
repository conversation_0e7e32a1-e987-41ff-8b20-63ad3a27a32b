package com.datascope.app.desensitize.processor;

import com.datascope.app.desensitize.core.AbstractSensitiveDataProcessor;
import com.datascope.app.desensitize.enums.SensitiveDataType;
import org.springframework.stereotype.Component;

/**
 * CVV脱敏处理器
 * 完全屏蔽
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class CvvProcessor extends AbstractSensitiveDataProcessor {
    
    /**
     * CVV最小长度
     */
    private static final int MIN_LENGTH = 3;
    
    /**
     * CVV最大长度
     */
    private static final int MAX_LENGTH = 4;
    
    @Override
    public SensitiveDataType getSupportedType() {
        return SensitiveDataType.CVV;
    }
    
    @Override
    protected String doProcess(String originalData) {
        return generateMask(originalData.length());
    }
    
    @Override
    public boolean isValid(String data) {
        return isLengthValid(data, MIN_LENGTH, MAX_LENGTH) && isNumeric(data);
    }
} 