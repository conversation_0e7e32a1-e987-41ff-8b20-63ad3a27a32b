package com.datascope.app.common.enums;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 模块类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ModuleTypeEnum {

    /**
     * QUERY
     */
    QUERY("QUERY", "QUERY"),

    /**
     * INTERATION
     */
    INTERATION("INTERATION", "INTERATION");

    private final String code;
    private final String desc;

    public static final String OPERATOR_MT = "请去流程魔方申请当前环境下的data-scope系统权限, 地址: {url}, 申请权限: 【{name}】";

    ModuleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
