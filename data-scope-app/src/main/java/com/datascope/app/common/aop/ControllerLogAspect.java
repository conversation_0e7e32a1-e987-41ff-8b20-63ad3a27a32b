package com.datascope.app.common.aop;

import com.datascope.app.common.response.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

@Aspect
@Component
public class ControllerLogAspect {
    private static final Logger log = LoggerFactory.getLogger(ControllerLogAspect.class);
    private static final String[] SENSITIVE_FIELDS = {"password", "token", "secret"};

    @Around("@within(restController) && !within(com.datascope.app.controller.ExcelExportController)")
    public Object logControllerAccess(ProceedingJoinPoint joinPoint, RestController restController) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();

        // 跳过非 Controller 的方法
        if (!StringUtils.endsWith(signature.getDeclaringTypeName(), "Controller")) {
            return joinPoint.proceed();
        }

        // 过滤敏感字段
        ObjectMapper mapper = new ObjectMapper();
        FilterProvider filters = new SimpleFilterProvider()
            .addFilter("sensitiveFilter",
                SimpleBeanPropertyFilter.serializeAllExcept(SENSITIVE_FIELDS));

        String argsJson = mapper.writer(filters).writeValueAsString(joinPoint.getArgs());

        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Throwable e) {
            log.warn("", e);
            result = Response.error(e.getMessage());
        } finally {
            log.info("类={}.{}\n请求参数={}\n返回结果={}",
                signature.getDeclaringTypeName(),
                signature.getName(),
                argsJson,
                result);
        }
        return result;
    }
}
