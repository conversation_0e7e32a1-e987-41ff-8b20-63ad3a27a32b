package com.datascope.app.common.response;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用响应类
 *
 * @param <T> 响应数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Response<T> {

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR_CODE = 500;

    @Builder.Default
    private Boolean success = true;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 创建成功响应
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Response<T> ok() {
        return Response.<T>builder()
            .success(true)
            .code(SUCCESS_CODE)
            .message("操作成功")
            .build();
    }

    /**
     * 创建成功响应
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 成功响应
     */
    public static <T> Response<T> ok(T data) {
        return Response.<T>builder()
            .success(true)
            .code(SUCCESS_CODE)
            .message("操作成功")
            .data(data)
            .build();
    }

    /**
     * 创建成功响应
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> Response<T> ok(String message, T data) {
        return Response.<T>builder()
            .success(true)
            .code(SUCCESS_CODE)
            .message(message)
            .data(data)
            .build();
    }

    /**
     * 创建错误响应
     *
     * @param <T> 数据类型
     * @return 错误响应
     */
    public static <T> Response<T> error() {
        return Response.<T>builder()
            .success(false)
            .code(ERROR_CODE)
            .message("操作失败")
            .build();
    }

    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> Response<T> error(String message) {
        return Response.<T>builder()
            .success(false)
            .code(ERROR_CODE)
            .message(StrUtil.isBlank(message) ? "操作失败" : message)
            .build();
    }

    /**
     * 创建错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> Response<T> error(int code, String message) {
        return Response.<T>builder()
            .code(code)
            .success(false)
            .message(message)
            .build();
    }
}
