package com.datascope.app.config.anno;

import com.datascope.app.common.enums.AuthTypeEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据源权限校验注解
 * 支持SpEL表达式来动态获取数据源ID或名称
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CheckDataSourcePermission {

    /**
     * 数据源ID或名称的SpEL表达式
     * 可以从方法参数、返回值或Bean中获取
     * 例如：
     * - "#datasourceId" (从方法参数获取)
     * - "#request.datasourceId" (从参数对象的属性获取)
     * - "@datasourceService.getDatasourceById(#id)" (调用Bean的方法)
     *
     * @return SpEL表达式
     */
    String value();

    /**
     * 权限校验失败时的错误消息
     *
     * @return 错误消息
     */
    String message() default "没有当前数据源下信息元数据修改权限";

    /**
     * 是否为数据源名称，默认false表示为数据源ID
     *
     * @return 是否为数据源名称
     */
    boolean isDatasourceName() default false;

    /**
     * 权限类型，默认为数据源权限
     *
     * @return 权限类型
     */
    AuthTypeEnum type() default AuthTypeEnum.DATASOURCE;

    /**
     * 是否为操作数据源权限
     *
     * @return 是否为操作数据源权限
     */
     boolean operatorDataSource() default false;
}
