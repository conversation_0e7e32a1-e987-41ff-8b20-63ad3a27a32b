package com.datascope.app.config;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datascope.app.common.BusinessException;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.common.enums.ModuleTypeEnum;
import com.datascope.app.config.anno.CheckDataSourcePermission;
import com.datascope.app.constants.CommonConst;
import com.datascope.app.constants.Constant;
import com.datascope.app.entity.Column;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.service.AuthService;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.util.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.expression.BeanFactoryAccessor;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.BeanFactory;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据源权限校验切面
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Aspect
public class DataSourcePermissionAspect {

    @Autowired
    private AuthService authService;

    @Autowired
    private DatasourceService datasourceService;

    @Autowired
    private BeanFactory beanFactory;

    @Autowired
    private TableMapper tableMapper;

    @Autowired
    private ColumnMapper columnMapper;

    @Autowired
    private SchemaMapper schemaMapper;

    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    /**
     * 参数名称发现器
     */
    private final DefaultParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 拦截带有@CheckDataSourcePermission注解的方法
     *
     * @param joinPoint 连接点
     * @param checkPermission 注解实例
     */
    @Before("@annotation(checkPermission)")
    public void checkDataSourcePermission(JoinPoint joinPoint, CheckDataSourcePermission checkPermission) {
        try {
            // 获取当前登录用户
            String loginName = AuthUtils.getLoginName();
            if (StrUtil.isBlank(loginName)) {
                log.warn("未获取到当前登录用户信息");
                throw new BusinessException("401", "用户未登录");
            }

            // 解析SpEL表达式获取数据源标识
            String datasourceIdentifier = parseDatasourceIdentifier(joinPoint, checkPermission.value());
            if (StrUtil.isBlank(datasourceIdentifier)) {
                log.warn("无法从SpEL表达式中获取数据源标识: {}", checkPermission.value());
                throw new BusinessException("400", "数据源标识不能为空");
            }

            // 获取数据源信息
            Datasource datasource = getDatasource(datasourceIdentifier, checkPermission.isDatasourceName(), checkPermission.type());
            if (datasource == null) {
                log.warn("未找到数据源: {}", datasourceIdentifier);
                throw new BusinessException("404", "数据源不存在");
            }

            // 进行权限校验
            List<String> userRoles = authService.getUserRoles(loginName);

            // && !userRoles.contains("sysadmin")
            if (checkPermission.operatorDataSource()) {
                if (userRoles.contains(Constant.Resource.DB_CREATE)) {
                    return;
                }
                throw new BusinessException("500", CommonConst.errorMessage("data-scope数据源创建", checkPermission.message()));
            }
            if (!userRoles.contains("db:role:" + datasource.getDatabaseName() + ":update")) {
                log.warn("用户 {} 没有数据源 {} 的访问权限", loginName, datasource.getDatabaseName());
                throw new BusinessException("500", CommonConst.errorMessage("数据源: "
                    + datasource.getDatabaseName() + "-更新角色", checkPermission.message()));
            }

            log.info("用户 {} 数据源权限校验通过: {}", loginName, datasource.getName());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("数据源权限校验异常", e);
            throw new BusinessException("500", "数据源权限校验失败: " + e.getMessage());
        }
    }

    /**
     * 解析SpEL表达式获取数据源标识
     *
     * @param joinPoint 连接点
     * @param spelExpression SpEL表达式
     * @return 数据源标识
     */
    private String parseDatasourceIdentifier(JoinPoint joinPoint, String spelExpression) {
        try {
            // 获取方法和参数信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            // 创建SpEL上下文
            StandardEvaluationContext context = new MethodBasedEvaluationContext(
                joinPoint.getTarget(), method, args, parameterNameDiscoverer);

            // 添加Bean工厂访问器，支持@beanName的方式访问Spring Bean
            context.addPropertyAccessor(new BeanFactoryAccessor());
            context.setBeanResolver(new BeanFactoryResolver(beanFactory));

            // 解析表达式
            Expression expression = expressionParser.parseExpression(spelExpression);
            Object result = expression.getValue(context);

            return result != null ? result.toString() : null;

        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", spelExpression, e);
            throw new BusinessException("400", "SpEL表达式解析失败: " + e.getMessage());
        }
    }

    /**
     * 根据标识获取数据源
     *
     * @param identifier 数据源ID或名称
     * @param isName 是否为数据源名称
     * @return 数据源对象
     */
    private Datasource getDatasource(String identifier, boolean isName, AuthTypeEnum authTypeEnum) {
        try {
            if (isName) {
                // 根据名称查询数据源，使用MyBatis Plus的条件查询
                return datasourceService.lambdaQuery()
                    .eq(Datasource::getName, identifier)
                    .one();
            } else {
                switch (authTypeEnum) {
                    case TABLE:
                        // 根据表名查询数据源
                        Table table = tableMapper.selectById(identifier);
                        if (Objects.isNull(table)) {
                           throw new BusinessException("404", "表不存在");
                        }
                        return datasourceService.getById(table.getDatasourceId());
                    case COLUMN:
                        // 根据列名查询数据源
                        Column column = columnMapper.selectById(identifier);
                        if (Objects.isNull(column)) {
                            throw new BusinessException("404", "字段不存在");
                        }
                        LambdaQueryWrapper<Table> wrapperTable = new LambdaQueryWrapper<>();
                        wrapperTable.eq(Table::getId, column.getTableId());
                        Table tableBean = tableMapper.selectOne(wrapperTable);
                        if (Objects.isNull(tableBean)) {
                            throw new BusinessException("404", "表不存在");
                        }
                        return datasourceService.getById(tableBean.getDatasourceId());
                    case SCHEMA:
                        // 根据模式名查询数据源
                        Schema schema = schemaMapper.selectById(identifier);
                        if (Objects.isNull(schema)) {
                            throw new BusinessException("404", "schema不存在");
                        }
                        return datasourceService.getById(schema.getDatasourceId());
                    default:
                        // 默认根据ID查询数据源
                        return datasourceService.getById(identifier);
                }
            }
        } catch (Exception e) {
            log.error("获取数据源失败: {}", identifier, e);
            return null;
        }
    }
}
