package com.datascope.app.controller;

import com.datascope.app.dto.excel.ExportCustomRequest;
import com.datascope.app.dto.query.ExecuteQueryParams;
import com.datascope.app.service.ExcelExportService;
import com.datascope.app.dto.excel.ExcelHeader;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresAuthorization;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导出控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/excel")
@Tag(name = "Excel导出模块", description = "Excel导出相关接口")
public class ExcelExportController {

    private final ExcelExportService excelExportService;

    /**
     * 导出查询结果
     */
    @PostMapping("/{queryId}/versions/{versionId}/{integrationId}/export")
    @Operation(summary = "导出查询结果", description = "根据查询ID导出Excel文件")
    @RequiresAuthorization(required = false)
    public void exportQueryResult(
                                @PathVariable("queryId") String queryId,
                                @PathVariable("versionId") String versionId,
                                @PathVariable("integrationId") String integrationId,
                                @RequestBody(required = false) Map<String, Object> params,
                                @RequestParam(required = false) String sort,
                                HttpServletResponse response) {

        log.info("导出查询结果: queryId={}, versionId={}, integrationId={}", queryId, versionId, integrationId);
        ExecuteQueryParams executeQueryParams = new ExecuteQueryParams();
        executeQueryParams.setParameters(params);
        executeQueryParams.setSort(sort);
        excelExportService.exportQueryData(response, queryId, versionId, integrationId, executeQueryParams);
    }

    /**
     * 自定义动态表头导出
     */
    @PostMapping("/export-custom")
    @Operation(summary = "自定义动态表头导出", description = "根据自定义表头配置导出Excel文件")
    public void exportWithCustomHeaders(
            @RequestParam String fileName,
            @RequestBody ExportCustomRequest request,
            HttpServletResponse response) {

        log.info("自定义导出Excel: fileName={}, headers={}, dataSize={}",
                fileName, request.getHeaders().size(),
                request.getData() != null ? request.getData().size() : 0);

        excelExportService.exportWithDynamicHeaders(response, fileName, request.getHeaders(), request.getData());
    }


    /**
     * 测试基本导出功能
     */
    @GetMapping("/test")
    @Operation(summary = "测试Excel导出", description = "测试基本的Excel导出功能")
    @RequiresAuthorization(required = false)
    @RequiresGuest
    public void testExport(HttpServletResponse response) {
        log.info("测试Excel导出功能");

        // 创建测试表头
        List<ExcelHeader> headers = Arrays.asList(
            ExcelHeader.builder().key("id").title("ID").width(15).build(),
            ExcelHeader.builder().key("name").title("姓名").width(20).build(),
            ExcelHeader.builder().key("age").title("年龄").width(10).build(),
            ExcelHeader.builder().key("email").title("邮箱").width(30).build(),
            ExcelHeader.builder().key("createTime").title("创建时间").width(20).build()
        );

        // 创建测试数据
        List<Map<String, Object>> testData = Arrays.asList(
            createTestDataMap(1, "张三", 25, "<EMAIL>", "2024-01-01 10:00:00"),
            createTestDataMap(2, "李四", 30, "<EMAIL>", "2024-01-02 11:00:00"),
            createTestDataMap(3, "王五", 28, "<EMAIL>", "2024-01-03 12:00:00")
        );

        excelExportService.exportWithDynamicHeaders(response, "测试导出", headers, testData);
    }

    /**
     * 创建测试数据Map
     */
    private Map<String, Object> createTestDataMap(int id, String name, int age, String email, String createTime) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("name", name);
        data.put("age", age);
        data.put("email", email);
        data.put("createTime", createTime);
        return data;
    }
}
