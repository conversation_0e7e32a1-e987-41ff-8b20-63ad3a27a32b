package com.datascope.app.controller;

import com.datascope.app.common.enums.ModuleTypeEnum;
import com.datascope.app.common.response.PageResponse;
import com.datascope.app.common.response.Response;
import com.datascope.app.config.anno.CheckDataSourcePermission;
import com.datascope.app.dto.datasource.*;
import com.datascope.app.service.AuthService;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.util.AuthUtils;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据源管理控制器
 */
@RestController
@RequestMapping("/api/datasources")
@Tag(name = "数据源管理模块")
public class DatasourceController {

    @Autowired
    private DatasourceService datasourceService;

    @Autowired
    private AuthService authService;

    /**
     * 获取数据源列表
     *
     * @param param 查询参数
     * @return 数据源列表
     */
    @GetMapping
    public Response<PageResponse<DataSourceDTO>> listDatasources(DataSourceQueryParam param) {
        List<String> dataSourceNames = authService.getDataSourceNames(AuthUtils.getLoginName(), param.getModuleKey());
        param.setDataSourceNames(dataSourceNames);
        List<DataSourceDTO> datasources = datasourceService.listDatasources(param);
        long total = datasourceService.countDatasources(param);
        PageResponse<DataSourceDTO> pageResponse = PageResponse.of(datasources, param.getPage(), param.getSize(), total);
        return Response.ok(pageResponse);
    }

    /**
     * 根据ID获取数据源
     *
     * @param id 数据源ID
     * @return 数据源信息
     */
    @GetMapping("/{id}")
    public Response<DataSourceDTO> getDatasource(@PathVariable("id") String id) {
        DataSourceDTO datasource = datasourceService.getDatasourceById(id);
        if (datasource != null) {
            return Response.ok(datasource);
        } else {
            return Response.error("数据源不存在");
        }
    }

    /**
     * 创建数据源
     *
     * @param request 创建请求
     * @return 创建后的数据源
     */
    @PostMapping
    public Response<DataSourceDTO> createDatasource(@Valid @RequestBody CreateDataSourceRequest request) {
        DataSourceDTO datasource = datasourceService.createDatasource(request);
        return Response.ok("数据源创建成功", datasource);
    }

    /**
     * 更新数据源
     *
     * @param id      数据源ID
     * @param request 更新请求
     * @return 更新后的数据源
     */
    @PutMapping("/{id}")
    @CheckDataSourcePermission(value = "#id", operatorDataSource = true, message = "没有编辑该数据源的权限")
    public Response<DataSourceDTO> updateDatasource(@PathVariable("id") String id, @Valid @RequestBody UpdateDataSourceRequest request) {
        // 确保ID一致
        request.setId(id);
        DataSourceDTO datasource = datasourceService.updateDatasource(request);
        if (datasource != null) {
            return Response.ok("数据源更新成功", datasource);
        } else {
            return Response.error("数据源不存在");
        }
    }

    /**
     * 删除数据源
     *
     * @param id 数据源ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @CheckDataSourcePermission(value = "#id", operatorDataSource = true, message = "没有删除该数据源的权限")
    public Response<Boolean> deleteDatasource(@PathVariable("id") String id) {
        boolean success = datasourceService.deleteDatasource(id);
        if (success) {
            return Response.ok("数据源删除成功", true);
        } else {
            return Response.error("数据源不存在或删除失败");
        }
    }

    /**
     * 检查数据源状态
     *
     * @param id 数据源ID
     * @return 数据源状态
     */
    @GetMapping("/{id}/check-status")
    public Response<DataSourceStatusDTO> checkDatasourceStatus(@PathVariable("id") String id) {
        DataSourceStatusDTO status = datasourceService.checkDatasourceStatus(id);
        if (status != null) {
            return Response.ok(status);
        } else {
            return Response.error("数据源不存在");
        }
    }

    /**
     * 测试数据源连接
     *
     * @param id 数据源ID
     * @return 测试结果
     */
    @PostMapping("/{id}/test-connection")
    public Response<TestConnectionResultDTO> testConnection(@PathVariable("id") String id) {
        TestConnectionResultDTO result = datasourceService.testConnection(id);
        if (result != null) {
            return Response.ok(result);
        } else {
            return Response.error("数据源不存在");
        }
    }

    /**
     * 测试新数据源连接（未保存的数据源）
     *
     * @param request 包含连接信息的请求体
     * @return 测试结果
     */
    @PostMapping("/test-connection")
    public Response<TestConnectionResultDTO> testNewConnection(@RequestBody TestConnectionRequest request) {
        if (request == null) {
            return Response.error("请求参数不能为空");
        }
        TestConnectionResultDTO result = datasourceService.testNewConnection(request);
        return Response.ok(result);
    }

    /**
     * 获取数据源统计信息
     *
     * @param id 数据源ID
     * @return 统计信息
     */
    @GetMapping("/{id}/stats")
    public Response<DataSourceStatsDTO> getDatasourceStats(@PathVariable("id") String id) {
        DataSourceStatsDTO stats = datasourceService.getDatasourceStats(id);
        if (stats != null) {
            return Response.ok(stats);
        } else {
            return Response.error("数据源不存在或无统计信息");
        }
    }

    /**
     * 同步数据源元数据
     *
     * @param id      数据源ID
     * @param request 同步请求
     * @return 同步状态
     */
    @PostMapping("/{id}/sync")
    @CheckDataSourcePermission(value = "#id")
    public Response<SyncStatusDTO> syncDatasourceMetadata(@PathVariable("id") String id, @RequestBody(required = false) SyncDataSourceRequest request) {
        if (request == null) {
            request = new SyncDataSourceRequest();
        }
        SyncStatusDTO syncStatus = datasourceService.syncDatasourceMetadata(id, request);
        if (syncStatus != null) {
            return Response.ok("数据源同步已启动", syncStatus);
        } else {
            return Response.error("数据源不存在");
        }
    }

    @GetMapping("/handler/create-role")
    @RequiresGuest
    public Response<Void> handlerCreateRole() {
        this.datasourceService.handlerCreateRole(null);
        return Response.ok();
    }
}
