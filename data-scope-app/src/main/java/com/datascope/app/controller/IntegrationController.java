package com.datascope.app.controller;

import com.datascope.app.common.response.PageResponse;
import com.datascope.app.common.response.Response;
import com.datascope.app.dto.integration.*;
import com.datascope.app.service.IntegrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 集成管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/integrations")
@RequiredArgsConstructor
@Tag(name = "集成管理模块")
public class IntegrationController {

    private final IntegrationService integrationService;

    /**
     * 获取集成列表
     *
     * @param param 查询参数
     * @return 集成列表
     */
    @GetMapping
    @Operation(summary = "获取集成列表")
    public Response<PageResponse<IntegrationDTO>> getIntegrationList(IntegrationQueryParam param) {
        PageResponse<IntegrationDTO> pageResponse = integrationService.getIntegrationList(param);
        return Response.ok(pageResponse);
    }

    /**
     * 创建集成
     *
     * @param request 创建请求
     * @return 创建后的集成信息
     */
    @PostMapping
    @Operation(summary = "创建集成", description = "创建新的集成配置")
    public Response<IntegrationDTO> createIntegration(@Valid @RequestBody CreateIntegrationRequest request) {
        IntegrationDTO integrationDTO = integrationService.createIntegration(request);
        return Response.ok(integrationDTO);
    }

    /**
     * 根据ID获取集成详情
     *
     * @param id 集成ID
     * @return 集成详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取集成详情")
    public Response<IntegrationDTO> getIntegrationById(@PathVariable String id) {
        IntegrationDTO integrationDTO = integrationService.getIntegrationById(id);
        if (integrationDTO == null) {
            return Response.error("集成不存在");
        }
        return Response.ok(integrationDTO);
    }

    /**
     * 更新集成
     *
     * @param id      集成ID
     * @param request 更新请求
     * @return 更新后的集成信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新集成", description = "更新已有的集成配置")
    public Response<IntegrationDTO> updateIntegration(@PathVariable String id, @Valid @RequestBody UpdateIntegrationRequest request) {
        // 确保ID一致
        request.setId(id);
        IntegrationDTO integrationDTO = integrationService.updateIntegration(request);
        if (integrationDTO == null) {
            return Response.error("集成不存在");
        }
        return Response.ok(integrationDTO);
    }

    /**
     * 删除集成
     *
     * @param id 集成ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除集成")
    public Response<Void> deleteIntegration(@PathVariable String id) {
        boolean success = integrationService.deleteIntegration(id);
        if (!success) {
            return Response.error("集成不存在或删除失败");
        }
        return Response.ok("集成已成功删除", null);
    }

    /**
     * 预览集成
     *
     * @param id 集成ID
     * @return 预览数据
     */
    @GetMapping("/{id}/preview")
    @Operation(summary = "预览集成", description = "预览集成的数据结果")
    public Response<IntegrationQueryResultDTO> previewIntegration(@PathVariable String id) {
        IntegrationQueryResultDTO resultDTO = integrationService.previewIntegration(id);
        if (resultDTO == null) {
            return Response.error("集成不存在或预览失败");
        }
        return Response.ok(resultDTO);
    }

    /**
     * 更新集成状态
     *
     * @param id      集成ID
     * @param request 状态更新请求
     * @return 更新后的状态信息
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "更新集成状态", description = "更新集成的状态：ACTIVE、INACTIVE、DRAFT")
    public Response<IntegrationDTO> updateIntegrationStatus(@PathVariable String id, @Valid @RequestBody UpdateIntegrationStatusRequest request) {
        IntegrationDTO integrationDTO = integrationService.updateIntegrationStatus(id, request);
        if (integrationDTO == null) {
            return Response.error("集成不存在");
        }
        return Response.ok(integrationDTO);
    }

    /**
     * 执行集成查询
     *
     * @param request 执行请求
     * @return 查询结果
     */
    @PostMapping("/execute-query")
    @Operation(summary = "执行集成查询", description = "执行集成的查询并返回结果")
    public Response<IntegrationQueryResultDTO> executeIntegrationQuery(@Valid @RequestBody ExecuteIntegrationQueryRequest request) {
        IntegrationQueryResultDTO resultDTO = integrationService.executeIntegrationQuery(request);
        if (resultDTO == null) {
            return Response.error("执行查询失败");
        }
        return Response.ok(resultDTO);
    }

    /**
     * 执行集成查询（兼容旧路径）
     *
     * @param request 执行请求
     * @return 查询结果
     * @deprecated 请使用 /api/integrations/execute-query 路径
     */
    @Deprecated
    @PostMapping(value = "/api/integration/execute-query")
    @Operation(summary = "执行集成查询(兼容旧路径)", description = "兼容旧路径，请使用/api/integrations/execute-query", deprecated = true)
    public Response<IntegrationQueryResultDTO> executeIntegrationQueryCompat(@Valid @RequestBody ExecuteIntegrationQueryRequest request) {
        return executeIntegrationQuery(request);
    }

    /**
     * 更新集成URL
     *
     * @param id 集成ID
     * @param url 新的URL
     * @return 查询结果
     */
    @PutMapping(value = "/{id}/url-update", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "更新集成URL", description = "更新集成URL")
    public Response<Void> updateIntegrationUrl(@RequestBody String url, @PathVariable String id) {
        integrationService.updateIntegrationUrl(id, url);
        return Response.ok();
    }

}
