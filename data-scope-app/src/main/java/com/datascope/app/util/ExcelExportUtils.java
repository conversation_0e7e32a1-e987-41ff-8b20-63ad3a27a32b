package com.datascope.app.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.datascope.app.dto.excel.ColumnInfo;
import com.datascope.app.dto.excel.ExcelHeader;
import com.datascope.app.dto.excel.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel导出工具类
 * 支持动态表头导出，高效处理大数据量
 *
 * 性能优化说明：
 * 1. 使用静态DateTimeFormatter避免重复创建日期格式化器
 * 2. 将Date转换为LocalDateTime统一处理，避免ThreadLocal复杂性
 * 3. 使用传统for循环替代嵌套stream操作提高性能
 * 4. 添加列宽计算缓存减少重复计算
 * 5. 使用静态样式策略避免重复创建样式对象
 * 6. 针对小数据量使用直接写入方式减少ExcelWriter开销
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class ExcelExportUtils {

    private static final int BATCH_SIZE = 1000;
    private static final String DEFAULT_SHEET_NAME = "Sheet1";
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // 静态日期格式化器，避免重复创建
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT);

    // 列宽计算缓存，避免重复计算
    private static final Map<String, Integer> COLUMN_WIDTH_CACHE = new HashMap<>();

    // 静态样式策略，避免每次创建
    private static final HorizontalCellStyleStrategy CELL_STYLE_STRATEGY = createCellStyleStrategy();

    // 性能测试开关：true=使用极简模式（无样式），false=使用标准样式
    private static final boolean PERFORMANCE_TEST_MODE = false;

    /**
     * 动态表头导出
     *
     * @param response   HTTP响应
     * @param fileName   文件名（不含扩展名）
     * @param headers    表头配置
     * @param dataList   数据列表
     */
    public static void exportWithDynamicHeaders(HttpServletResponse response,
                                               String fileName,
                                               List<ExcelHeader> headers,
                                               List<Map<String, Object>> dataList) {
        try {
            setResponseHeaders(response, fileName);
            exportWithDynamicHeaders(response.getOutputStream(), headers, dataList, DEFAULT_SHEET_NAME);
        } catch (IOException e) {
            log.error("导出Excel失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 动态表头导出到流 - 性能优化版本
     *
     * @param outputStream 输出流
     * @param headers      表头配置
     * @param dataList     数据列表
     * @param sheetName    工作表名称
     */
    public static void exportWithDynamicHeaders(OutputStream outputStream,
                                               List<ExcelHeader> headers,
                                               List<Map<String, Object>> dataList,
                                               String sheetName) {
        StopWatch stopWatch = new StopWatch("exportWithDynamicHeaders");
        if (CollectionUtils.isEmpty(headers)) {
            throw new IllegalArgumentException("表头配置不能为空");
        }

        // 记录数据规模
        int headerCount = headers.size();
        int dataCount = dataList != null ? dataList.size() : 0;
        log.info("开始Excel导出：{}行数据，{}个字段", dataCount, headerCount);

        stopWatch.start("buildDynamicHeaders");
        // 构建动态表头
        List<List<String>> headerList = buildDynamicHeaders(headers);
        stopWatch.stop();
        log.info("构建表头完成，耗时: {}ms", stopWatch.getLastTaskTimeMillis());

        // 构建数据
        stopWatch.start("buildDataRows");
        List<List<Object>> dataRows = buildDataRows(headers, dataList);
        stopWatch.stop();
        log.info("构建数据行完成，耗时: {}ms", stopWatch.getLastTaskTimeMillis());

        // 优化：对于小数据量（<100行），使用简化的写入方式
        stopWatch.start("writeExcel");
        if (dataRows.size() < 100) {
            log.info("使用小数据量优化写入方式");
            writeSmallDataExcel(outputStream, headerList, dataRows, sheetName);
        } else {
            log.info("使用大数据量分批写入方式");
            writeLargeDataExcel(outputStream, headerList, dataRows, sheetName);
        }
        stopWatch.stop();
        log.info("Excel写入完成，耗时: {}ms", stopWatch.getLastTaskTimeMillis());
    }

    /**
     * 小数据量Excel写入（优化版本）
     */
    private static void writeSmallDataExcel(OutputStream outputStream,
                                           List<List<String>> headerList,
                                           List<List<Object>> dataRows,
                                           String sheetName) {
        try {
            if (PERFORMANCE_TEST_MODE) {
                // 性能测试模式：完全不使用样式策略
                log.info("使用极简模式导出（无样式）");
                writeMinimalExcel(outputStream, headerList, dataRows, sheetName);
            } else {
                // 正常模式：使用标准样式，保持一致性
                EasyExcel.write(outputStream)
                        .excelType(ExcelTypeEnum.XLSX)
                        .registerWriteHandler(CELL_STYLE_STRATEGY)
                        .sheet(sheetName)
                        .head(headerList)
                        .doWrite(dataRows);

                log.info("小数据量Excel导出成功，共导出{}行数据", dataRows.size());
            }
        } catch (Exception e) {
            log.error("小数据量Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 极简模式Excel写入（无样式，最快速度）
     * 用于性能测试和对比
     */
    public static void writeMinimalExcel(OutputStream outputStream,
                                        List<List<String>> headerList,
                                        List<List<Object>> dataRows,
                                        String sheetName) {
        try {
            // 完全不使用样式策略，追求最快速度
            EasyExcel.write(outputStream)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(sheetName)
                    .head(headerList)
                    .doWrite(dataRows);

            log.info("极简模式Excel导出成功，共导出{}行数据", dataRows.size());
        } catch (Exception e) {
            log.error("极简模式Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 大数据量Excel写入（分批处理）
     */
    private static void writeLargeDataExcel(OutputStream outputStream,
                                           List<List<String>> headerList,
                                           List<List<Object>> dataRows,
                                           String sheetName) {
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(CELL_STYLE_STRATEGY)
                .build()) {

            WriteSheet writeSheet = EasyExcel.writerSheet(0, sheetName)
                    .head(headerList)
                    .build();

            // 分批写入数据
            if (CollectionUtils.isNotEmpty(dataRows)) {
                int totalSize = dataRows.size();
                for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                    int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                    List<List<Object>> batchData = dataRows.subList(i, endIndex);
                    excelWriter.write(batchData, writeSheet);
                }
            } else {
                excelWriter.write(new ArrayList<>(), writeSheet);
            }

            // finish()方法会刷新缓冲区并完成写入
            excelWriter.finish();
            log.info("大数据量Excel导出成功，共导出{}行数据", dataRows.size());

        } catch (Exception e) {
            log.error("大数据量Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 基于SQL查询结果的动态导出 - 优化版本
     *
     * @param response     HTTP响应
     * @param fileName     文件名
     * @param queryResult  查询结果（包含表头和数据）
     */
    public static void exportFromQueryResult(HttpServletResponse response, String fileName, QueryResult queryResult) {
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getColumns())) {
            throw new IllegalArgumentException("查询结果不能为空");
        }

        long startTime = System.currentTimeMillis();

        // 将查询结果转换为表头配置 - 优化：避免重复计算
        List<ExcelHeader> headers = new ArrayList<>(queryResult.getColumns().size());
        for (ColumnInfo column : queryResult.getColumns()) {
            headers.add(ExcelHeader.builder()
                    .key(column.getName())
                    .title(StringUtils.isNotBlank(column.getComment()) ? column.getComment() : column.getName())
                    .width(calculateColumnWidth(column.getType()))
                    .build());
        }

        long headerBuildTime = System.currentTimeMillis();
        log.info("表头构建耗时: {}ms", headerBuildTime - startTime);

        exportWithDynamicHeaders(response, fileName, headers, queryResult.getData());

        long totalTime = System.currentTimeMillis();
        log.info("Excel导出总耗时: {}ms, 数据行数: {}, 列数: {}",
                totalTime - startTime,
                queryResult.getData() != null ? queryResult.getData().size() : 0,
                headers.size());
    }

    /**
     * 构建动态表头
     */
    private static List<List<String>> buildDynamicHeaders(List<ExcelHeader> headers) {
        return headers.stream()
                .map(header -> Collections.singletonList(header.getTitle()))
                .collect(Collectors.toList());
    }

    /**
     * 构建数据行 - 优化版本
     */
    private static List<List<Object>> buildDataRows(List<ExcelHeader> headers, List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        // 预先提取header的key列表，避免重复获取
        List<String> headerKeys = headers.stream()
                .map(ExcelHeader::getKey)
                .collect(Collectors.toList());

        // 使用传统for循环替代嵌套stream，提高性能
        List<List<Object>> result = new ArrayList<>(dataList.size());
        for (Map<String, Object> dataMap : dataList) {
            List<Object> row = new ArrayList<>(headerKeys.size());
            for (String key : headerKeys) {
                Object value = dataMap.get(key);
                row.add(formatCellValue(value));
            }
            result.add(row);
        }
        return result;
    }

    /**
     * 格式化单元格值 - 优化版本（方案3：Date转LocalDateTime）
     */
    private static Object formatCellValue(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof LocalDateTime) {
            // 使用静态的DateTimeFormatter，避免重复创建
            return ((LocalDateTime) value).format(DATE_TIME_FORMATTER);
        }

        if (value instanceof Date) {
            // 方案3：将Date转换为LocalDateTime，然后使用DateTimeFormatter
            // 优势：1. 避免ThreadLocal内存泄漏 2. 无需synchronized 3. 线程安全 4. 性能更好
            LocalDateTime localDateTime = ((Date) value).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            return localDateTime.format(DATE_TIME_FORMATTER);
        }

        // 处理java.sql.Timestamp（继承自Date，但单独处理以确保兼容性）
        if (value instanceof java.sql.Timestamp) {
            LocalDateTime localDateTime = ((java.sql.Timestamp) value).toLocalDateTime();
            return localDateTime.format(DATE_TIME_FORMATTER);
        }

        // 处理java.sql.Date
        if (value instanceof java.sql.Date) {
            LocalDateTime localDateTime = ((java.sql.Date) value).toLocalDate()
                    .atStartOfDay();
            return localDateTime.format(DATE_TIME_FORMATTER);
        }

        return value;
    }

    /**
     * 创建单元格样式策略 - 性能优化版本
     */
    private static HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 头部样式 - 保持原有视觉效果，优化创建过程
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Arial");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 优化：统一设置边框样式，减少重复调用
        BorderStyle borderStyle = BorderStyle.THIN;
        headWriteCellStyle.setBorderTop(borderStyle);
        headWriteCellStyle.setBorderBottom(borderStyle);
        headWriteCellStyle.setBorderLeft(borderStyle);
        headWriteCellStyle.setBorderRight(borderStyle);

        // 内容样式 - 保持原有视觉效果，优化创建过程
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("Arial");
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 优化：统一设置边框样式，减少重复调用
        contentWriteCellStyle.setBorderTop(borderStyle);
        contentWriteCellStyle.setBorderBottom(borderStyle);
        contentWriteCellStyle.setBorderLeft(borderStyle);
        contentWriteCellStyle.setBorderRight(borderStyle);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 设置响应头
     */
    private static void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
//        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");
        response.setHeader("Content-Disposition", "attachment; filename*=" + encodedFileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    /**
     * 根据数据类型计算列宽 - 优化版本（带缓存）
     */
    private static Integer calculateColumnWidth(String dataType) {
        if (StringUtils.isBlank(dataType)) {
            return 20;
        }

        // 从缓存中获取，避免重复计算
        return COLUMN_WIDTH_CACHE.computeIfAbsent(dataType, type -> {
            String lowerType = type.toLowerCase();
            if (lowerType.contains("varchar") || lowerType.contains("text")) {
                return 25;
            } else if (lowerType.contains("datetime") || lowerType.contains("timestamp")) {
                return 20;
            } else if (lowerType.contains("decimal") || lowerType.contains("number")) {
                return 15;
            } else {
                return 15;
            }
        });
    }
}
