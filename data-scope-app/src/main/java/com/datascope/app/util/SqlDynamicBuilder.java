package com.datascope.app.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.datascope.app.model.FieldRule;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL动态构建工具类
 * 根据用户设置的字段规则，动态构建SQL查询条件
 */
public class SqlDynamicBuilder {

    // SQL关键字模式，用于识别SQL中的WHERE子句位置
    private static final Pattern WHERE_PATTERN = Pattern.compile("\\s+WHERE\\s+", Pattern.CASE_INSENSITIVE);
    private static final Pattern ORDER_BY_PATTERN = Pattern.compile("\\s+ORDER\\s+BY\\s+", Pattern.CASE_INSENSITIVE);
    private static final Pattern GROUP_BY_PATTERN = Pattern.compile("\\s+GROUP\\s+BY\\s+", Pattern.CASE_INSENSITIVE);
    private static final Pattern HAVING_PATTERN = Pattern.compile("\\s+HAVING\\s+", Pattern.CASE_INSENSITIVE);
    private static final Pattern LIMIT_PATTERN = Pattern.compile("\\s+LIMIT\\s+", Pattern.CASE_INSENSITIVE);

    // 表别名识别模式
    private static final Pattern TABLE_ALIAS_PATTERN = Pattern.compile("\\b(\\w+)\\.(\\w+)\\b");

    // 表名、表别名关系模式 - 支持 database.table 格式
    private static final Pattern TABLE_NAME_ALIAS_PATTERN = Pattern.compile("\\s+((?:\\w+\\.)?\\w+)\\s+(?:as\\s+)?(\\w+)\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern TABLE_JOIN_PATTERN = Pattern.compile("\\b(from|join)\\s+((?:\\w+\\.)?\\w+)(?:\\s+(\\w+))?", Pattern.CASE_INSENSITIVE);

    // 直接表引用模式（表名.字段）
    private static final Pattern DIRECT_TABLE_REF_PATTERN = Pattern.compile("\\b(\\w+)\\.(\\w+)\\b");

    // 日期区间分隔符模式
    private static final Pattern DATE_RANGE_SEPARATOR = Pattern.compile("\\s*,\\s*");

    // SQL关键字列表，避免将这些误识别为表名或别名
    private static final Set<String> SQL_KEYWORDS = new HashSet<>(Arrays.asList(
        "select", "from", "where", "join", "inner", "outer", "left", "right", "on",
        "and", "or", "not", "in", "between", "like", "is", "null", "group", "order",
        "by", "having", "limit", "offset", "union", "except", "intersect"
    ));

    /**
     * 根据基础SQL、字段规则和查询参数构建完整的SQL
     *
     * @param baseSql 基础SQL语句
     * @param fieldRules 字段规则列表
     * @param queryParams 查询参数Map
     * @return 构建后的完整SQL
     */
    public static String buildSql(String baseSql, List<FieldRule> fieldRules, Map<String, Object> queryParams) {
        if (baseSql == null || baseSql.isEmpty()) {
            throw new IllegalArgumentException("基础SQL不能为空");
        }

        if (fieldRules == null || fieldRules.isEmpty() || MapUtils.isEmpty(queryParams)) {
            return baseSql; // 没有字段规则，直接返回基础SQL
        }

        // 解析SQL中的表名和表别名映射关系，用于用户只设置了表名但没有设置表别名的情况
        Map<String, String> tableNameToAliasMap = parseTableAliases(baseSql);

        // 解析直接使用表名的情况（如"queries.name"），验证表名是否直接在SQL中使用
        Set<String> directTableRefs = findDirectTableReferences(baseSql);

        // 根据SQL中的信息更新字段规则中的表名和表别名
        updateFieldRulesWithTableInfo(fieldRules, tableNameToAliasMap, directTableRefs);

        // 构建WHERE条件
        List<String> conditions = buildConditions(fieldRules, queryParams);

        if (conditions.isEmpty()) {
            return baseSql; // 没有条件，直接返回基础SQL
        }

        // 处理SQL子句位置
        int wherePos = findPosition(baseSql, WHERE_PATTERN);
        int orderByPos = findPosition(baseSql, ORDER_BY_PATTERN);
        int groupByPos = findPosition(baseSql, GROUP_BY_PATTERN);
        int havingPos = findPosition(baseSql, HAVING_PATTERN);
        int limitPos = findPosition(baseSql, LIMIT_PATTERN);

        // 提取SQL各部分
        String beforeWhere = "";
        String afterWhere = "";
        String whereClause = "";
        String otherClauses = "";

        // 分解SQL
        if (wherePos > 0) {
            beforeWhere = baseSql.substring(0, wherePos);

            // 找到WHERE子句后的其他子句的开始位置
            int endWherePos = baseSql.length();
            if (orderByPos > wherePos && orderByPos < endWherePos) endWherePos = orderByPos;
            if (groupByPos > wherePos && groupByPos < endWherePos) endWherePos = groupByPos;
            if (havingPos > wherePos && havingPos < endWherePos) endWherePos = havingPos;
            if (limitPos > wherePos && limitPos < endWherePos) endWherePos = limitPos;

            // 提取WHERE子句和其他子句
            whereClause = baseSql.substring(wherePos + 6, endWherePos).trim();
            otherClauses = endWherePos < baseSql.length() ? baseSql.substring(endWherePos) : "";

            // 处理已存在的字段条件 - 只替换在queryParams中有值的字段的条件
            Set<String> fieldsToReplace = getFieldsWithValues(fieldRules, queryParams);
            if (!fieldsToReplace.isEmpty()) {
                whereClause = removeExistingFieldConditions(whereClause, fieldsToReplace);
            }
        } else {
            beforeWhere = baseSql;
            whereClause = "";

            // 提取其他子句
            int firstClausePos = baseSql.length();
            if (orderByPos > 0 && orderByPos < firstClausePos) firstClausePos = orderByPos;
            if (groupByPos > 0 && groupByPos < firstClausePos) firstClausePos = groupByPos;
            if (havingPos > 0 && havingPos < firstClausePos) firstClausePos = havingPos;
            if (limitPos > 0 && limitPos < firstClausePos) firstClausePos = limitPos;

            if (firstClausePos < baseSql.length()) {
                beforeWhere = baseSql.substring(0, firstClausePos);
                otherClauses = baseSql.substring(firstClausePos);
            }
        }

        // 构建SQL
        StringBuilder sqlBuilder = new StringBuilder(beforeWhere);

        // 添加WHERE子句
        if (wherePos > 0) {
            sqlBuilder.append(" WHERE ");
            if (!whereClause.isEmpty()) {
                sqlBuilder.append(whereClause).append(" AND ");
            }
        } else {
            sqlBuilder.append(" WHERE ");
        }

        // 添加新条件
        sqlBuilder.append(String.join(" AND ", conditions));

        // 添加其他子句
        if (!otherClauses.isEmpty()) {
            sqlBuilder.append(" ").append(otherClauses);
        }

        return sqlBuilder.toString();
    }

    /**
     * 获取在查询参数中有值的字段名集合
     *
     * @param fieldRules 字段规则列表
     * @param queryParams 查询参数Map
     * @return 在查询参数中有值的字段名集合
     */
    private static Set<String> getFieldsWithValues(List<FieldRule> fieldRules, Map<String, Object> queryParams) {
        Set<String> fields = new HashSet<>();
        for (FieldRule rule : fieldRules) {
            String fieldName = rule.getFieldName();
            Object value = queryParams.get(fieldName);

            // 只添加在查询参数中有值的字段
            if (value != null && !(value instanceof String && ((String) value).isEmpty())) {
                fields.add(fieldName);
            }
        }
        return fields;
    }

    /**
     * 从WHERE子句中移除已存在的字段条件
     *
     * @param whereClause WHERE子句内容（不包含"WHERE"关键字）
     * @param fields 需要移除的字段集合
     * @return 处理后的WHERE子句内容
     */
    private static String removeExistingFieldConditions(String whereClause, Set<String> fields) {
        String processedWhereClause = whereClause;

        // 构建正则表达式模式，匹配字段条件
        List<Pattern> fieldPatterns = new ArrayList<>();
        for (String field : fields) {
            // 匹配 field = value 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s*=\\s*('[^']*'|\\d+|NULL|null|TRUE|true|FALSE|false)", Pattern.CASE_INSENSITIVE));

            // 匹配 field > value, field < value, field >= value, field <= value 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s*[><]=?\\s*('[^']*'|\\d+|NULL|null|TRUE|true|FALSE|false)", Pattern.CASE_INSENSITIVE));

            // 匹配 field in (values) 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s+IN\\s*\\([^)]*\\)", Pattern.CASE_INSENSITIVE));

            // 匹配 field like pattern 格式 - 包含多种LIKE格式
            // 匹配 LIKE CONCAT('%', 'value', '%') 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s+LIKE\\s+CONCAT\\s*\\([^)]*\\)", Pattern.CASE_INSENSITIVE));
            // 匹配 LIKE '%value%' 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s+LIKE\\s+('[^']*'|\"[^\"]*\")", Pattern.CASE_INSENSITIVE));

            // 匹配 field between value1 and value2 格式
            fieldPatterns.add(Pattern.compile("\\b" + field + "\\s+BETWEEN\\s+[^\\s]+\\s+AND\\s+[^\\s]+", Pattern.CASE_INSENSITIVE));

            // 匹配带表名或别名前缀的字段 = 格式
            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s*=\\s*('[^']*'|\\d+|NULL|null|TRUE|true|FALSE|false)", Pattern.CASE_INSENSITIVE));

            // 匹配带表名或别名前缀的字段 > < >= <= 格式
            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s*[><]=?\\s*('[^']*'|\\d+|NULL|null|TRUE|true|FALSE|false)", Pattern.CASE_INSENSITIVE));

            // 匹配带表名或别名前缀的字段其他格式
            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s+IN\\s*\\([^)]*\\)", Pattern.CASE_INSENSITIVE));

            // 匹配带表名或别名前缀的LIKE格式
            // 匹配 LIKE CONCAT('%', 'value', '%') 格式
            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s+LIKE\\s+CONCAT\\s*\\([^)]*\\)", Pattern.CASE_INSENSITIVE));
            // 匹配 LIKE '%value%' 格式
            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s+LIKE\\s+('[^']*'|\"[^\"]*\")", Pattern.CASE_INSENSITIVE));

            fieldPatterns.add(Pattern.compile("\\b\\w+\\." + field + "\\s+BETWEEN\\s+[^\\s]+\\s+AND\\s+[^\\s]+", Pattern.CASE_INSENSITIVE));
        }

        // 依次处理每个字段条件
        for (Pattern pattern : fieldPatterns) {
            Matcher matcher = pattern.matcher(processedWhereClause);
            StringBuffer sb = new StringBuffer();

            while (matcher.find()) {
                // 找到匹配的条件
                String condition = matcher.group(0);
                // 替换为空字符串
                matcher.appendReplacement(sb, "");
            }
            matcher.appendTail(sb);
            processedWhereClause = sb.toString();
        }

        // 清理多余的 AND 和 OR，以及表名或别名的残余部分
        processedWhereClause = processedWhereClause
            .replaceAll("(?i)\\s+AND\\s+AND\\s+", " AND ")
            .replaceAll("(?i)\\s+OR\\s+OR\\s+", " OR ")
            .replaceAll("(?i)^\\s*AND\\s+", "")
            .replaceAll("(?i)^\\s*OR\\s+", "")
            .replaceAll("(?i)\\s+AND\\s*$", "")
            .replaceAll("(?i)\\s+OR\\s*$", "")
            .replaceAll("\\b\\w+\\.\\s+AND\\b", "") // 移除像 "table. AND" 这样的残余
            .replaceAll("\\b\\w+\\.\\s+OR\\b", "") // 移除像 "table. OR" 这样的残余
            .replaceAll("\\b\\w+\\.\\s*$", "") // 移除行末的 "table." 残余
            .trim();

        return processedWhereClause;
    }

    /**
     * 从完整的表名（可能包含数据库名）中提取表名部分
     * 例如：tl.TBL_RRS_REQ -> TBL_RRS_REQ
     *
     * @param fullName 完整的表名
     * @return 表名部分
     */
    private static String extractTableNameFromFullName(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return fullName;
        }

        // 如果包含点号，取最后一部分作为表名
        int lastDotIndex = fullName.lastIndexOf('.');
        if (lastDotIndex != -1 && lastDotIndex < fullName.length() - 1) {
            return fullName.substring(lastDotIndex + 1);
        }

        return fullName;
    }

    /**
     * 解析SQL中的表名和表别名映射关系
     *
     * @param sql SQL语句
     * @return 表名到表别名的映射
     */
    private static Map<String, String> parseTableAliases(String sql) {
        Map<String, String> tableNameToAliasMap = new HashMap<>();

        try {
            // 预处理SQL：移除注释和字符串字面量中的干扰内容
            String cleanSql = preprocessSql(sql);

            // 提取FROM子句中的表名和别名
            // 支持 database.table 格式，支持 AS 关键字，支持无别名情况
            Pattern fromPattern = Pattern.compile(
                "\\bFROM\\s+((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?(?:\\s|,|$)",
                Pattern.CASE_INSENSITIVE
            );
            Matcher fromMatcher = fromPattern.matcher(cleanSql);

            if (fromMatcher.find()) {
                String fullTableName = fromMatcher.group(1);
                String tableName = extractTableNameFromFullName(fullTableName);
                String alias = fromMatcher.group(2);

                if (alias != null && !alias.isEmpty() && !SQL_KEYWORDS.contains(alias.toLowerCase())) {
                    tableNameToAliasMap.put(tableName.toLowerCase(), alias);
                }
            }

            // 提取JOIN子句中的表名和别名
            // 支持各种JOIN类型：LEFT JOIN, RIGHT JOIN, INNER JOIN, FULL JOIN等
            Pattern joinPattern = Pattern.compile(
                "\\b(?:LEFT|RIGHT|INNER|FULL|CROSS)?\\s*(?:OUTER\\s+)?JOIN\\s+((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?(?:\\s|$)",
                Pattern.CASE_INSENSITIVE
            );
            Matcher joinMatcher = joinPattern.matcher(cleanSql);

            while (joinMatcher.find()) {
                String fullTableName = joinMatcher.group(1);
                String tableName = extractTableNameFromFullName(fullTableName);
                String alias = joinMatcher.group(2);

                if (alias != null && !alias.isEmpty() && !SQL_KEYWORDS.contains(alias.toLowerCase())) {
                    tableNameToAliasMap.put(tableName.toLowerCase(), alias);
                }
            }

        } catch (Exception e) {
            // 解析失败时记录错误但不抛出异常，返回空映射
            System.err.println("解析SQL表别名失败: " + e.getMessage());
        }

        return tableNameToAliasMap;
    }

    /**
     * 预处理SQL，移除注释和字符串字面量中可能干扰解析的内容
     *
     * @param sql 原始SQL
     * @return 清理后的SQL
     */
    private static String preprocessSql(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 移除单行注释 --
        sql = sql.replaceAll("--.*?(?=\\r?\\n|$)", "");

        // 移除多行注释 /* */
        sql = sql.replaceAll("/\\*.*?\\*/", "");

        // 移除字符串字面量中的内容，避免干扰表名解析
        // 这里简单处理，将字符串内容替换为占位符
        sql = sql.replaceAll("'[^']*'", "''");
        sql = sql.replaceAll("\"[^\"]*\"", "\"\"");

        return sql;
    }

    /**
     * 查找SQL中直接使用表名引用字段的情况（如"queries.name"）
     *
     * @param sql SQL语句
     * @return 直接引用的表名集合
     */
    private static Set<String> findDirectTableReferences(String sql) {
        Set<String> directTableRefs = new HashSet<>();

        Matcher matcher = DIRECT_TABLE_REF_PATTERN.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1);
            if (!SQL_KEYWORDS.contains(tableName.toLowerCase())) {
                directTableRefs.add(tableName.toLowerCase());
            }
        }

        return directTableRefs;
    }

    /**
     * 根据表名找到表别名，更新字段规则
     *
     * @param fieldRules 字段规则列表
     * @param tableNameToAliasMap 表名到表别名的映射
     * @param directTableRefs 直接引用的表名集合
     */
    private static void updateFieldRulesWithTableInfo(
        List<FieldRule> fieldRules,
        Map<String, String> tableNameToAliasMap,
        Set<String> directTableRefs) {

        for (FieldRule rule : fieldRules) {
            // 如果表别名是SQL关键字，清除它
            if (rule.getTableAlias() != null && SQL_KEYWORDS.contains(rule.getTableAlias().toLowerCase())) {
                rule.setTableAlias(null);
            }

            // 如果表名是SQL关键字，清除它
            if (rule.getTableName() != null && SQL_KEYWORDS.contains(rule.getTableName().toLowerCase())) {
                rule.setTableName(null);
            }

            // 如果已有表别名，则不需要更新
            if (rule.getTableAlias() != null && !rule.getTableAlias().isEmpty()) {
                continue;
            }

            // 如果有表名但没有表别名
            if (rule.getTableName() != null && !rule.getTableName().isEmpty()) {
                String tableName = rule.getTableName().toLowerCase();

                // 检查是否有对应的别名
                String tableAlias = tableNameToAliasMap.get(tableName);
                if (tableAlias != null && !tableAlias.isEmpty()) {
                    // 如果找到了别名，设置别名
                    rule.setTableAlias(tableAlias);
                } else if (directTableRefs.contains(tableName)) {
                    // 如果在直接引用的表名集合中，保持使用表名
                    // getFullFieldName()方法会直接使用tableName
                } else {
                    // 如果表名既没有别名也没有直接引用，可能是用户错误
                    // 我们保持不变，允许用户决定是否需要这样的字段前缀
                }
            }
        }
    }

    /**
     * 构建查询条件列表
     *
     * @param fieldRules 字段规则列表
     * @param queryParams 查询参数Map
     * @return 条件列表
     */
    private static List<String> buildConditions(List<FieldRule> fieldRules, Map<String, Object> queryParams) {
        List<String> conditions = new ArrayList<>();

        for (FieldRule rule : fieldRules) {
            String fieldName = rule.getFieldName();
            Object value = queryParams.get(fieldName);

            // 如果值为空且不是必填字段，则跳过
            if (value == null || (value instanceof String && ((String) value).isEmpty())) {
                if (!rule.isRequired()) {
                    continue;
                } else {
                    throw new IllegalArgumentException("必填字段 " + fieldName + " 的值不能为空");
                }
            }

            // 根据字段规则构建条件
            String condition = buildCondition(rule, value);
            if (condition != null && !condition.isEmpty()) {
                conditions.add(condition);
            }
        }

        return conditions;
    }

    /**
     * 根据字段规则和值构建单个条件
     *
     * @param rule 字段规则
     * @param value 字段值
     * @return 构建的条件
     */
    private static String buildCondition(FieldRule rule, Object value) {
        String fullFieldName = rule.getFullFieldName();
        String formType = rule.getFormType();

        // 处理多选
        if (Objects.nonNull(rule.getIsMultiSelect()) && rule.getIsMultiSelect() && value instanceof String) {
            String valueStr = (String) value;
            if (valueStr.contains(StrPool.COMMA)) {
                String[] values = valueStr.split("\\s*,\\s*");
                // 构建IN条件
                return buildInCondition(fullFieldName, values);
            }
        }

        // 根据表单类型处理
        if ("date-range".equalsIgnoreCase(formType) && value instanceof String) {
            return buildDateRangeCondition(fullFieldName, (String) value);
        } else if ("date".equalsIgnoreCase(formType)) {
            return buildDateCondition(fullFieldName, value);
        } else if (Objects.nonNull(rule.getIsFuzzyMatch()) && rule.getIsFuzzyMatch() && value instanceof String) {
            return buildFuzzyCondition(fullFieldName, (String) value);
        } else if ("number_range".equalsIgnoreCase(formType) && value instanceof String) {
            return buildNumberRangeCondition(fullFieldName, (String) value);
        }else {
            return buildExactCondition(fullFieldName, value);
        }
    }

    /**
     * 构建日期区间条件
     *
     * @param fieldName 字段名
     * @param dateRange 日期区间字符串，格式如：2025-05-06,2025-05-08
     * @return 构建的条件
     */
    private static String buildDateRangeCondition(String fieldName, String dateRange) {
        String[] dates = DATE_RANGE_SEPARATOR.split(dateRange.trim());
        // 兼容
        if (dates.length == 0) {
            return "";
        }
        if (dates.length != 2) {
            throw new IllegalArgumentException("日期区间格式不正确: " + dateRange);
        }

        String startDate = dates[0].trim();
        String endDate = dates[1].trim();

        // 构建日期区间条件
        StringBuilder condition = new StringBuilder();
        condition.append(fieldName).append(" >= '").append(startDate).append("'")
            .append(" AND ")
            .append(fieldName).append(" <= '").append(endDate).append("'");

        return condition.toString();
    }

    /**
     * 构建日期条件
     *
     * @param fieldName 字段名
     * @param value 日期值
     * @return 构建的条件
     */
    private static String buildDateCondition(String fieldName, Object value) {
        return fieldName + " = '" + value + "'";
    }

    /**
     * 构建模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value 查询值
     * @return 构建的条件
     */
    private static String buildFuzzyCondition(String fieldName, String value) {
        // 转义单引号
        String escapedValue = value.replace("'", "''");
        return fieldName + " LIKE CONCAT('%', '" + escapedValue + "', '%')";
    }

    /**
     * 构建数字区间条件
     *
     * @param fieldName 字段名
     * @param value 数字区间字符串，格式如：1,5
     * @return 构建的条件
     */
    private static String buildNumberRangeCondition(String fieldName, String value) {
        String[] range = DATE_RANGE_SEPARATOR.split(value.trim());
        // 兼容
        if (range.length == 0) {
            return "";
        }
        if (range.length != 2 || Arrays.stream(range).anyMatch(StrUtil::isBlank)) {
            return "";
        }


        String startNumber = range[0].trim();
        String endNumber = range[1].trim();
        boolean startFlag = NumberUtil.isNumber(startNumber);
        boolean endFlag = NumberUtil.isNumber(endNumber);
        Assert.isTrue(startFlag && endFlag, "数字范围必须都是数字: " + Arrays.toString(range));

        return fieldName + " between '" + startNumber + " AND " + endNumber;
    }

    /**
     * 构建精确匹配条件
     *
     * @param fieldName 字段名
     * @param value 查询值
     * @return 构建的条件
     */
    private static String buildExactCondition(String fieldName, Object value) {
        if (value instanceof String) {
            // 转义单引号
            String escapedValue = ((String) value).replace("'", "''");
            return fieldName + " = '" + escapedValue + "'";
        } else if (value instanceof Number) {
            return fieldName + " = " + value;
        } else if (value instanceof Boolean) {
            return fieldName + " = " + (((Boolean) value) ? "TRUE" : "FALSE");
        } else {
            return fieldName + " = '" + value + "'";
        }
    }

    /**
     * 构建IN条件
     *
     * @param fieldName 字段名
     * @param values 多个值
     * @return 构建的条件
     */
    private static String buildInCondition(String fieldName, String[] values) {
        if (values.length == 0) {
            return null;
        }

        StringBuilder condition = new StringBuilder();
        condition.append(fieldName).append(" IN (");

        // 判断是否是数字
        boolean isNumeric = true;
        for (String value : values) {
            try {
                Double.parseDouble(value.trim());
            } catch (NumberFormatException e) {
                isNumeric = false;
                break;
            }
        }

        for (int i = 0; i < values.length; i++) {
            String value = values[i].trim();
            if (isNumeric) {
                condition.append(value);
            } else {
                // 转义单引号
                String escapedValue = value.replace("'", "''");
                condition.append("'").append(escapedValue).append("'");
            }

            if (i < values.length - 1) {
                condition.append(", ");
            }
        }

        condition.append(")");
        return condition.toString();
    }

    /**
     * 查找模式在SQL中的位置
     *
     * @param sql SQL语句
     * @param pattern 要查找的模式
     * @return 找到的位置，-1表示未找到
     */
    private static int findPosition(String sql, Pattern pattern) {
        Matcher matcher = pattern.matcher(sql);
        if (matcher.find()) {
            return matcher.start();
        }
        return -1;
    }

    /**
     * 解析表别名和字段名
     * 例如：t.name -> table:t, field:name
     *
     * @param fullFieldName 完整字段名
     * @return 数组[表别名, 字段名]，如果没有表别名则第一个元素为null
     */
    public static String[] parseFieldName(String fullFieldName) {
        Matcher matcher = TABLE_ALIAS_PATTERN.matcher(fullFieldName);
        if (matcher.matches()) {
            return new String[]{matcher.group(1), matcher.group(2)};
        }
        return new String[]{null, fullFieldName};
    }
}
