package com.datascope.app.util;

import cn.hutool.core.text.StrPool;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class SqlUtil {

    // 预编译正则表达式，提升性能
    private static final Pattern SELECT_PATTERN = Pattern.compile("^\\s*select\\s", Pattern.CASE_INSENSITIVE);
    private static final Pattern FORBIDDEN_KEYWORDS_PATTERN = Pattern.compile(
        "\\b(insert|update|delete|drop|create|alter|truncate|grant|revoke|commit|rollback|savepoint)\\b",
        Pattern.CASE_INSENSITIVE
    );

    public static String removeSemicolon(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        String trimmedSql = sql.trim();
        if (trimmedSql.endsWith(";")) {
            return trimmedSql.substring(0, trimmedSql.length() - 1);
        }
        return sql;
    }

    /**
     * 判断字符串是否为合法的SELECT SQL语句
     *
     * @param sql 待检查的SQL字符串
     * @return true表示是合法的SELECT语句，false表示不是
     */
    public static boolean isValidSelectSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String trimmedSql = sql.trim();

        // 使用预编译正则表达式快速检查是否以SELECT开头
        if (!SELECT_PATTERN.matcher(trimmedSql).find()) {
            return false;
        }

        // 使用预编译正则表达式检查禁止的关键字
        if (FORBIDDEN_KEYWORDS_PATTERN.matcher(trimmedSql).find()) {
            return false;
        }

        // 检查是否包含多个独立的SELECT语句
        if (hasMultipleStatementsOptimized(trimmedSql)) {
            return false;
        }

        // 检查SELECT关键字出现的次数（排除子查询中的SELECT）
        return countTopLevelSelectsOptimized(trimmedSql) <= 1;
    }

    /**
     * 优化的多语句检查 - 使用字符数组直接操作
     */
    private static boolean hasMultipleStatementsOptimized(String sql) {
        char[] chars = sql.toCharArray();
        boolean inString = false;
        char stringChar = 0;
        boolean foundSemicolon = false;

        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];

            // 处理字符串字面量
            if ((c == '\'' || c == '"') && (i == 0 || chars[i - 1] != '\\')) {
                if (!inString) {
                    inString = true;
                    stringChar = c;
                } else if (c == stringChar) {
                    inString = false;
                }
                continue;
            }

            if (inString) continue;

            if (c == ';') {
                foundSemicolon = true;
                break; // 找到第一个分号就可以确定有多个语句
            }
        }

        return foundSemicolon;
    }

    /**
     * 优化的顶层SELECT计数 - 使用字符数组直接操作
     */
    private static int countTopLevelSelectsOptimized(String sql) {
        char[] chars = sql.toCharArray();
        int count = 0;
        int parenLevel = 0;
        boolean inString = false;
        char stringChar = 0;

        for (int i = 0; i < chars.length - 6; i++) {
            char c = chars[i];

            // 处理字符串字面量
            if ((c == '\'' || c == '"') && (i == 0 || chars[i - 1] != '\\')) {
                if (!inString) {
                    inString = true;
                    stringChar = c;
                } else if (c == stringChar) {
                    inString = false;
                }
                continue;
            }

            if (inString) {
                continue;
            }

            // 处理括号
            if (c == '(') {
                parenLevel++;
            } else if (c == ')') {
                parenLevel--;
            }

            // 只在顶层检查SELECT
            if (parenLevel == 0 && isSelectKeyword(chars, i)) {
                count++;
            }
        }

        return count;
    }

    /**
     * 检查指定位置是否为SELECT关键字
     */
    private static boolean isSelectKeyword(char[] chars, int start) {
        if (start + 6 >= chars.length) return false;

        // 检查是否为"select "（不区分大小写）
        return (chars[start] == 's' || chars[start] == 'S') &&
               (chars[start + 1] == 'e' || chars[start + 1] == 'E') &&
               (chars[start + 2] == 'l' || chars[start + 2] == 'L') &&
               (chars[start + 3] == 'e' || chars[start + 3] == 'E') &&
               (chars[start + 4] == 'c' || chars[start + 4] == 'C') &&
               (chars[start + 5] == 't' || chars[start + 5] == 'T') &&
               Character.isWhitespace(chars[start + 6]);
    }

    /**
     * 快速检查是否以SELECT开头（不区分大小写）
     */
    private static boolean isSelectStart(String sql) {
        if (sql.length() < 6) return false;

        // 跳过前导空白字符
        int start = 0;
        while (start < sql.length() && Character.isWhitespace(sql.charAt(start))) {
            start++;
        }

        if (start + 5 >= sql.length()) return false;

        // 检查"SELECT"（不区分大小写）
        String prefix = sql.substring(start, start + 6).toLowerCase();
        return "select".equals(prefix);
    }

    /**
     * 检查是否包含禁止的关键字
     */
    private static boolean containsForbiddenKeywords(String sql) {
        String lowerSql = sql.toLowerCase();

        // 使用更高效的关键字检查
        String[] keywords = {
            "insert", "update", "delete", "drop", "create", "alter", "truncate",
            "grant", "revoke", "commit", "rollback", "savepoint"
        };

        for (String keyword : keywords) {
            if (lowerSql.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含多个语句
     */
    private static boolean hasMultipleStatements(String sql) {
        int semicolonCount = 0;
        boolean inString = false;
        char stringChar = 0;

        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);

            // 处理字符串
            if ((c == '\'' || c == '"') && (i == 0 || sql.charAt(i - 1) != '\\')) {
                if (!inString) {
                    inString = true;
                    stringChar = c;
                } else if (c == stringChar) {
                    inString = false;
                }
                continue;
            }

            if (inString) continue;

            if (c == ';') {
                semicolonCount++;
            }
        }

        return semicolonCount > 0;
    }

    /**
     * 统计顶层SELECT语句的数量（排除子查询中的SELECT）
     *
     * @param sql 标准化的SQL字符串
     * @return SELECT语句的数量
     */
    private static int countTopLevelSelects(String sql) {
        int count = 0;
        int parenLevel = 0;
        boolean inString = false;
        char stringChar = 0;

        for (int i = 0; i < sql.length() - 6; i++) {
            char c = sql.charAt(i);

            // 处理字符串
            if ((c == '\'' || c == '"') && (i == 0 || sql.charAt(i - 1) != '\\')) {
                if (!inString) {
                    inString = true;
                    stringChar = c;
                } else if (c == stringChar) {
                    inString = false;
                }
                continue;
            }

            if (inString) {
                continue;
            }

            // 处理括号
            if (c == '(') {
                parenLevel++;
            } else if (c == ')') {
                parenLevel--;
            }

            // 只在顶层检查SELECT
            if (parenLevel == 0 && sql.substring(i, i + 7).equals("select ")) {
                count++;
            }
        }

        return count;
    }
}
