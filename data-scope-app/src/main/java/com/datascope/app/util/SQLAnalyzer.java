package com.datascope.app.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datascope.app.dto.query.QueryFieldDTO;
import com.datascope.app.dto.query.QueryParameterDTO;
import com.datascope.app.entity.Column;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Table;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.TableMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SQL分析器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SQLAnalyzer {

    private final TableMapper tableMapper;
    private final ColumnMapper columnMapper;
    private final ObjectMapper objectMapper;

    /**
     * 提取SQL中的参数
     */
    public List<QueryParameterDTO> extractParameters(String sql) {
        List<QueryParameterDTO> parameters = new ArrayList<>();

        try {
            // 识别 MyBatis if 标签内部的参数，使用更宽松的匹配模式
            Pattern ifPattern = Pattern.compile("<if[^>]*?>(.*?)</if>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher ifMatcher = ifPattern.matcher(sql);
            Set<String> ifParams = new HashSet<>();

            while (ifMatcher.find()) {
                String ifContent = ifMatcher.group(1);
                // 在 if 标签内容中匹配参数，同时处理可能的换行和空白
                Pattern paramPattern = Pattern.compile("#\\{([^}]+)\\}|:([^\\s,\\)]+)");
                Matcher paramMatcher = paramPattern.matcher(ifContent);
                while (paramMatcher.find()) {
                    String param = paramMatcher.group(1);
                    if (param == null) {
                        param = paramMatcher.group(2);
                    }
                    if (param != null) {
                        // 去除可能的空白字符
                        param = param.trim();
                        ifParams.add(param);
                    }
                }
            }

            // 预处理：移除 MyBatis XML 标签中的内容
            String noXmlSql = preprocessMybatisXml(sql);

            // 记录所有参数占位符的位置
            List<ParameterPosition> paramPositions = new ArrayList<>();

            // 首先识别可选参数模式：(#{paramName} IS NULL OR ...)
            Pattern optionalPattern = Pattern.compile("\\(\\s*#\\{(\\w+)\\}\\s+IS\\s+NULL\\s+OR\\s+", Pattern.CASE_INSENSITIVE);
            Matcher optionalMatcher = optionalPattern.matcher(noXmlSql);

            // 记录哪些参数是可选的
            Set<String> optionalParams = new HashSet<>();
            while (optionalMatcher.find()) {
                String paramName = optionalMatcher.group(1);
                optionalParams.add(paramName);
            }

            // 再识别可选参数另一种模式：(:paramName IS NULL OR ...)
            Pattern optionalColonPattern = Pattern.compile("\\(\\s*:(\\w+)\\s+IS\\s+NULL\\s+OR\\s+", Pattern.CASE_INSENSITIVE);
            Matcher optionalColonMatcher = optionalColonPattern.matcher(noXmlSql);

            while (optionalColonMatcher.find()) {
                String paramName = optionalColonMatcher.group(1);
                optionalParams.add(paramName);
            }

            // 匹配:paramName形式的参数
            Pattern colonPattern = Pattern.compile(":(\\w+)");
            Matcher colonMatcher = colonPattern.matcher(noXmlSql);

            while (colonMatcher.find()) {
                String paramName = colonMatcher.group(1);
                int start = colonMatcher.start();
                int end = colonMatcher.end();

                // 检查参数是否在字符串字面量内部，如果是则跳过
                if (isInStringLiteral(noXmlSql, start)) {
                    continue;
                }

                // 记录参数位置
                paramPositions.add(new ParameterPosition(start, end));

                // 检查是否已存在
                if (parameters.stream().noneMatch(p -> p.getName().equals(paramName))) {
                    QueryParameterDTO param = QueryParameterDTO.builder()
                        .id(UUID.randomUUID().toString().replace("-", ""))
                        .name(paramName)
                        .type("string") // 默认类型
                        .label(paramName)
                        // .label(camelCaseToTitleCase(paramName))
                        .required(!optionalParams.contains(paramName) && !ifParams.contains(paramName)) // 根据参数是否可选或在if标签中设置required
                        .build();
                    parameters.add(param);
                }
            }

            // 匹配#{paramName}形式的参数
            Pattern mybatisPattern = Pattern.compile("#\\{(\\w+)\\}");
            Matcher mybatisMatcher = mybatisPattern.matcher(sql);

            while (mybatisMatcher.find()) {
                String paramName = mybatisMatcher.group(1);
                // 记录参数位置
                paramPositions.add(new ParameterPosition(mybatisMatcher.start(), mybatisMatcher.end()));

                // 检查是否已存在
                if (parameters.stream().noneMatch(p -> p.getName().equals(paramName))) {
                    QueryParameterDTO param = QueryParameterDTO.builder()
                        .id(UUID.randomUUID().toString().replace("-", ""))
                        .name(paramName)
                        .type("string") // 默认类型
                        .label(camelCaseToTitleCase(paramName))
                        .required(!optionalParams.contains(paramName) && !ifParams.contains(paramName)) // 根据参数是否可选或在if标签中设置required
                        .build();
                    parameters.add(param);
                }
            }

            // 如果已经找到参数占位符，检查它们是否在字符串字面量中
            if (!paramPositions.isEmpty()) {
                // 匹配所有字符串字面量
                Pattern stringPattern = Pattern.compile("(['\"]).*?\\1");
                Matcher stringMatcher = stringPattern.matcher(sql);

                while (stringMatcher.find()) {
                    int start = stringMatcher.start();
                    int end = stringMatcher.end();

                    // 检查每个参数占位符是否在这个字符串内
                    for (int i = 0; i < paramPositions.size(); i++) {
                        ParameterPosition pos = paramPositions.get(i);
                        if (pos.start >= start && pos.end <= end) {
                            // 参数在字符串中，标记为已处理
                            pos.processed = true;
                        }
                    }
                }

                // 如果所有参数都在字符串字面量中，跳过字面量参数识别
                boolean allParamsInString = paramPositions.stream().allMatch(p -> p.processed);
                if (allParamsInString) {
                    // 所有参数都在字符串中，返回当前参数列表，跳过字面量识别
                    return parameters;
                }
            }

            // 匹配字符串字面量 %字符串% 形式的参数 (主要用于LIKE语句)
            // 只匹配包含在单引号或双引号中的字面量
            Pattern likePattern = Pattern.compile("\\s+like\\s+['\"]%?([^'\"]+)%?['\"]", Pattern.CASE_INSENSITIVE);
            Matcher likeMatcher = likePattern.matcher(sql);

            while (likeMatcher.find()) {
                String value = likeMatcher.group(1);
                String paramName = "searchTerm"; // 默认参数名

                // 检查这个字符串内是否包含参数占位符
                boolean containsPlaceholder = false;
                for (ParameterPosition pos : paramPositions) {
                    if (likeMatcher.start(1) <= pos.start && pos.end <= likeMatcher.end(1)) {
                        containsPlaceholder = true;
                        break;
                    }
                }

                // 如果不包含参数占位符，则添加为searchTerm参数
                if (!containsPlaceholder && !value.contains("#{") && !value.contains(":")) {
                    if (parameters.stream().noneMatch(p -> p.getName().equals(paramName))) {
                        QueryParameterDTO param = QueryParameterDTO.builder()
                            .id(UUID.randomUUID().toString().replace("-", ""))
                            .name(paramName)
                            .type("string")
                            .label("搜索关键词")
                            .defaultValue(value) // 将当前值设为默认值
                            .required(true)
                            .build();
                        parameters.add(param);
                    }
                }
            }

            // 匹配等值比较中的字符串字面量
            Pattern equalPattern = Pattern.compile("(\\w+)\\s*=\\s*['\"]([^'\"]+)['\"]", Pattern.CASE_INSENSITIVE);
            Matcher equalMatcher = equalPattern.matcher(sql);

            while (equalMatcher.find()) {
                String columnName = equalMatcher.group(1);
                String value = equalMatcher.group(2);

                // 检查这个字符串内是否包含参数占位符
                boolean containsPlaceholder = false;
                for (ParameterPosition pos : paramPositions) {
                    if (equalMatcher.start(2) <= pos.start && pos.end <= equalMatcher.end(2)) {
                        containsPlaceholder = true;
                        break;
                    }
                }

                // 如果不包含参数占位符，则添加为列名参数
                /*if (!containsPlaceholder && !value.contains("#{") && !value.contains(":")) {
                    String paramName = columnName + "Value"; // 以列名为基础生成参数名

                    // 只有当值不是日期/时间格式的情况下才提取为参数
                    if (!isDateTimeFormat(value)) {
                        if (parameters.stream().noneMatch(p -> p.getName().equals(paramName))) {
                            QueryParameterDTO param = QueryParameterDTO.builder()
                                .id(UUID.randomUUID().toString().replace("-", ""))
                                .name(paramName)
                                .type("string")
                                .label(camelCaseToTitleCase(columnName) + " 值")
                                .defaultValue(value) // 将当前值设为默认值
                                .required(true)
                                .build();
                            parameters.add(param);
                        }
                    }
                }*/
            }

        } catch (Exception e) {
            log.error("解析SQL参数失败: {}", e.getMessage(), e);
        }

        return parameters;
    }

    /**
     * 预处理 MyBatis XML，保留标签中的 SQL 内容但移除标签属性
     */
    private String preprocessMybatisXml(String sql) {
        // 匹配 MyBatis XML 标签，如 <if test="...">, <foreach collection="...">, <where> 等
        Pattern xmlTagPattern = Pattern.compile("<(if|foreach|where|set|choose|when|otherwise|trim)\\s+[^>]*>(.*?)</\\1>|<(if|foreach|where|set|choose|when|otherwise|trim)[^>]*/?>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher xmlTagMatcher = xmlTagPattern.matcher(sql);

        // 使用 StringBuilder 来构建处理后的 SQL
        StringBuilder processedSql = new StringBuilder();
        int lastEnd = 0;

        while (xmlTagMatcher.find()) {
            // 添加标签之前的内容
            processedSql.append(sql.substring(lastEnd, xmlTagMatcher.start()));

            // 获取标签内的 SQL 内容（如果有）
            String tagContent = xmlTagMatcher.group(2);
            if (tagContent != null) {
                // 移除标签内容中可能存在的嵌套标签的属性
                tagContent = tagContent.replaceAll("<(if|foreach|where|set|choose|when|otherwise|trim)\\s+[^>]*>", "<$1>");
                // 添加处理后的标签内容
                processedSql.append(" ").append(tagContent.trim()).append(" ");
            }

            lastEnd = xmlTagMatcher.end();
        }

        // 添加剩余内容
        processedSql.append(sql.substring(lastEnd));

        return processedSql.toString();
    }

    /**
     * 检查字符串是否为日期时间格式
     */
    private boolean isDateTimeFormat(String value) {
        // 简单检查常见的日期时间格式
        String datePattern = "^\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}(\\s\\d{1,2}:\\d{1,2}(:\\d{1,2})?)?$";
        return value.matches(datePattern);
    }

    /**
     * 提取查询SQL中的字段
     */
    public List<QueryFieldDTO> extractSearchFields(String sql, Datasource dataSource) {

        sql = MybatisSqlParser.parseSql(sql, null);

        try {
            // 提取SELECT和FROM之间的部分
            Pattern pattern = Pattern.compile("SELECT\\s+(.+?)\\s+FROM", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(sql);

            if (matcher.find()) {
                // 获取SQL中的所有表名
                Set<String> tableNames = extractTableNames(sql);
                if (CollUtil.isNotEmpty(tableNames)) {
                    List<Table> tables = tableMapper.selectList(
                        new LambdaQueryWrapper<Table>()
                            .in(Table::getName, tableNames)
                            .eq(Table::getDatasourceId, dataSource.getId()));
                    if (CollUtil.isNotEmpty(tables)) {
                        List<Table> tableList = new ArrayList<>(tables.stream().collect(Collectors.groupingBy(Table::getName,
                            Collectors.collectingAndThen(Collectors.toList(),
                                value -> value.stream()
                                    .sorted(Comparator.comparing(Table::getUpdatedAt).reversed())
                                    .collect(Collectors.toList()).get(0)))).values());
                        List<String> tableIds = tableList.stream().map(Table::getId).collect(Collectors.toList());
                        Map<String, String> tableMap = tableList.stream().collect(Collectors.toMap(Table::getId, Table::getName));
                        List<Column> columns = columnMapper.selectList(new LambdaQueryWrapper<Column>().in(Column::getTableId, tableIds));
                        if (CollUtil.isNotEmpty(columns)) {
                            List<QueryFieldDTO> queryFieldDTOList = columns.stream()
                                .collect(Collectors.groupingBy(Column::getTableId,
                                    Collectors.collectingAndThen(Collectors.toList(),
                                        value -> value.stream()
                                            .sorted(Comparator.comparing(Column::getPosition))
                                            .collect(Collectors.toList())))).values().stream().map(columnList ->
                                                columnList.stream().map(column ->
                                                QueryFieldDTO.builder()
                                                    .name(column.getName())
                                                    .type(convertColumnTypeToFieldType(column.getDataType()))
                                                    .label(column.getDescription())
                                                    .tableName(tableMap.getOrDefault(column.getTableId(), null))
                                                    .isEncrypted(column.getIsEncrypted())
                                                    .formatType(column.getFormatType())
                                                    .desensitizeFlag(column.getAuthDesensitize())
                                                    .build()).collect(Collectors.toList()))
                                                    .flatMap(Collection::stream).collect(Collectors.toList());
                            log.info("extractSearchFields queryFieldDTOList: {}", objectMapper.writeValueAsString(queryFieldDTOList));
                            return queryFieldDTOList;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("parse extractSearchFields: ", e);
        }
        return Lists.newArrayList();
    }

    /**
     * 解析字段表达式，正确处理包含函数的复杂表达式
     */
    private List<String> parseFieldExpressions(String fieldsPart) {
        List<String> expressions = new ArrayList<>();
        StringBuilder currentExpression = new StringBuilder();
        int parenthesesLevel = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < fieldsPart.length(); i++) {
            char c = fieldsPart.charAt(i);

            // 处理引号
            if ((c == '\'' || c == '"' || c == '`') && !inQuotes) {
                inQuotes = true;
                quoteChar = c;
                currentExpression.append(c);
            } else if (c == quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = 0;
                currentExpression.append(c);
            } else if (inQuotes) {
                currentExpression.append(c);
            } else if (c == '(') {
                parenthesesLevel++;
                currentExpression.append(c);
            } else if (c == ')') {
                parenthesesLevel--;
                currentExpression.append(c);
            } else if (c == ',' && parenthesesLevel == 0) {
                // 只有在括号层级为0时，逗号才是字段分隔符
                expressions.add(currentExpression.toString().trim());
                currentExpression = new StringBuilder();
            } else {
                currentExpression.append(c);
            }
        }

        // 添加最后一个表达式
        if (currentExpression.length() > 0) {
            expressions.add(currentExpression.toString().trim());
        }

        return expressions;
    }

    /**
     * 从字段表达式中提取实际的字段名（处理函数包装的情况）
     */
    private List<String> extractFieldNamesFromExpression(String expression) {
        List<String> fieldNames = new ArrayList<>();

        // 移除AS别名部分
        String cleanExpression = expression;
        if (expression.toLowerCase().contains(" as ")) {
            String[] parts = expression.split("(?i) as ");
            cleanExpression = parts[0].trim();
        }

        // 使用正则表达式匹配字段名模式：表别名.字段名
        Pattern fieldPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\b");
        Matcher matcher = fieldPattern.matcher(cleanExpression);

        while (matcher.find()) {
            String fieldName = matcher.group(2); // 只取字段名部分，不包括表别名
            if (!fieldNames.contains(fieldName)) {
                fieldNames.add(fieldName);
            }
        }

        // 如果没有找到表别名.字段名的模式，尝试匹配单独的字段名
        if (fieldNames.isEmpty()) {
            Pattern simpleFieldPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\b");
            Matcher simpleMatcher = simpleFieldPattern.matcher(cleanExpression);

            while (simpleMatcher.find()) {
                String fieldName = simpleMatcher.group(1);
                // 排除SQL关键字和函数名
                if (!isSqlKeywordOrFunction(fieldName) && !fieldNames.contains(fieldName)) {
                    fieldNames.add(fieldName);
                }
            }
        }

        return fieldNames;
    }

    /**
     * 检查是否为SQL关键字或函数名
     */
    private boolean isSqlKeywordOrFunction(String word) {
        String upperWord = word.toUpperCase();
        Set<String> sqlKeywords = new HashSet<>();
        sqlKeywords.addAll(Arrays.asList(
            "SELECT", "FROM", "WHERE", "JOIN", "LEFT", "RIGHT", "INNER", "OUTER", "ON", "AS", "AND", "OR", "NOT",
            "IN", "EXISTS", "BETWEEN", "LIKE", "IS", "NULL", "TRUE", "FALSE", "ORDER", "BY", "GROUP", "HAVING",
            "LIMIT", "OFFSET", "UNION", "ALL", "DISTINCT", "COUNT", "SUM", "AVG", "MAX", "MIN", "COALESCE",
            "CONCAT", "SUBSTRING", "LENGTH", "UPPER", "LOWER", "TRIM", "CASE", "WHEN", "THEN", "ELSE", "END",
            "IF", "IFNULL", "ISNULL", "DATE", "TIME", "TIMESTAMP", "YEAR", "MONTH", "DAY", "HOUR", "MINUTE", "SECOND"
        ));
        return sqlKeywords.contains(upperWord);
    }

    /**
     * 提取SQL中的字段
     */
    public List<QueryFieldDTO> extractFields(String sql, Datasource dataSource) {
        sql = MybatisSqlParser.parseSql(sql, null);
        List<QueryFieldDTO> fields = new ArrayList<>();

        try {
            // 提取表名和别名的映射关系
            Map<String, String> tableAliasMap = extractTableAliasMap(sql);

            // 提取SELECT和FROM之间的部分
            Pattern pattern = Pattern.compile("SELECT\\s+(.+?)\\s+FROM", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(sql);

            if (matcher.find()) {
                String fieldsPart = matcher.group(1).trim();
                List<String> fieldExpressions = parseFieldExpressions(fieldsPart);

                for (String field : fieldExpressions) {
                    field = field.trim();

                    // 处理 * 的情况
                    if (field.equals("*")) {
                        Set<String> tableNames = extractTableNames(sql);
                        if (CollUtil.isNotEmpty(tableNames)) {
                            List<Table> tables = tableMapper.selectList(
                                new LambdaQueryWrapper<Table>()
                                    .in(Table::getName, tableNames)
                                    .eq(Table::getDatasourceId, dataSource.getId()));
                            if (CollUtil.isNotEmpty(tables)) {
                                List<Table> tableList = new ArrayList<>(tables.stream().collect(Collectors.groupingBy(Table::getName,
                                    Collectors.collectingAndThen(Collectors.toList(),
                                        value -> value.stream()
                                            .sorted(Comparator.comparing(Table::getUpdatedAt).reversed())
                                            .collect(Collectors.toList()).get(0)))).values());
                                List<String> tableIds = tableList.stream().map(Table::getId).collect(Collectors.toList());
                                Map<String, String> tableMap = tableList.stream().collect(Collectors.toMap(Table::getId, Table::getName));
                                List<Column> columns = columnMapper.selectList(new LambdaQueryWrapper<Column>().in(Column::getTableId, tableIds));
                                if (CollUtil.isNotEmpty(columns)) {
                                    columns.stream()
                                        .collect(Collectors.groupingBy(Column::getTableId,
                                            Collectors.collectingAndThen(Collectors.toList(),
                                                value -> value.stream()
                                                    .sorted(Comparator.comparing(Column::getPosition))
                                                    .collect(Collectors.toList()))))
                                        .forEach((tableId, columnList) ->
                                            columnList.forEach(column -> {
                                                QueryFieldDTO build;
                                                try {
                                                    build = QueryFieldDTO.builder()
                                                        .name(column.getName())
                                                        .type(convertColumnTypeToFieldType(column.getDataType()))
                                                        .label(column.getDescription())
                                                        .tableName(tableMap.getOrDefault(column.getTableId(), null))
                                                        .isEncrypted(column.getIsEncrypted())
                                                        .entryConfig(StrUtil.isNotBlank(column.getEncryConfig())
                                                            ? objectMapper.readValue(column.getEncryConfig(), Object.class)
                                                            : null)
                                                        .build();
                                                } catch (JsonProcessingException e) {
                                                    throw new RuntimeException(e);
                                                }
                                                fields.add(build);
                                        }));
                                }
                            }
                        }
                        continue;
//                        for (String mainTableName : tableNames) {
//                            List<Table> tables = tableMapper.selectList(
//                                new LambdaQueryWrapper<Table>()
//                                    .eq(Table::getName, mainTableName)
//                                    .eq(Table::getDatasourceId, dataSource.getId())
//                                    .orderByDesc(Table::getUpdatedAt)
//                                    .last("LIMIT 1")
//                            );
//
//                            Table table = tables.isEmpty() ? null : tables.get(0);
//                            if (table != null) {
//                                List<Column> columns = columnMapper.selectList(
//                                    new LambdaQueryWrapper<Column>()
//                                        .eq(Column::getTableId, table.getId())
//                                        .orderByAsc(Column::getPosition)
//                                );
//
//                                for (Column column : columns) {
//                                    QueryFieldDTO fieldDTO = QueryFieldDTO.builder()
//                                        .name(column.getName())
//                                        .type(convertColumnTypeToFieldType(column.getDataType()))
//                                        .label(column.getDescription())
//                                        .tableName(mainTableName)
//                                        .isEncrypted(column.getIsEncrypted())
//                                        .build();
//                                    fields.add(fieldDTO);
//                                }
//                            }
//                        }

                    }

                    // 处理 table.* 的情况
                    if (field.endsWith(".*")) {
                        String tableAlias = field.substring(0, field.length() - 2);
                        String actualTableName = tableAliasMap.get(tableAlias);

                        if (actualTableName != null) {
                            List<Table> tables = tableMapper.selectList(new LambdaQueryWrapper<Table>()
                                    .eq(Table::getName, actualTableName)
                                    .eq(Table::getDatasourceId, dataSource.getId()));
                            tables = tables.stream().sorted(Comparator.comparing(Table::getUpdatedAt).reversed())
                                .collect(Collectors.toList());

                            Table table = tables.isEmpty() ? null : tables.get(0);
                            if (table != null) {
                                List<Column> columns = columnMapper
                                    .selectList(new LambdaQueryWrapper<Column>().eq(Column::getTableId, table.getId()));
                                columns = columns.stream()
                                    .sorted(Comparator.comparing(Column::getPosition))
                                    .collect(Collectors.toList());
                                for (Column column : columns) {
                                    QueryFieldDTO fieldDTO = QueryFieldDTO.builder()
                                        .name(column.getName())
                                        .type(convertColumnTypeToFieldType(column.getDataType()))
                                        .label(column.getDescription())
                                        .tableName(actualTableName)
                                        .isEncrypted(column.getIsEncrypted())
                                        .entryConfig(StrUtil.isNotBlank(column.getEncryConfig())
                                            ? objectMapper.readValue(column.getEncryConfig(), Object.class)
                                            : null)
                                        .build();
                                    fields.add(fieldDTO);
                                }
                            }
                        }
                    } else {
                        // 处理普通字段和包含函数的复杂表达式
                        String originalField = field;
                        String fieldLabel = field;

                        // 处理字段别名
                        if (field.toLowerCase().contains(" as ")) {
                            String[] parts = field.split("(?i) as ");
                            originalField = parts[0].trim();
                            if (parts.length > 1) {
                                fieldLabel = parts[1].trim().replaceAll("['\"`]", "");
                            }
                        }

                        // 处理字段名和表名
                        String fieldName = originalField;
                        String tableName = null;

                        // 如果有AS别名，使用别名作为字段名
                        if (field.toLowerCase().contains(" as ")) {
                            fieldName = fieldLabel; // 使用别名作为字段名
                        } else {
                            // 处理表名.字段名的形式
                            if (fieldName.contains(".")) {
                                String[] parts = fieldName.split("\\.");
                                if (parts.length == 2) {
                                    String tableAlias = parts[0];
                                    fieldName = parts[1];
                                    tableName = tableAliasMap.get(tableAlias);
                                }
                            } else {
                                // 如果字段没有指定表名，使用第一个表
                                if (!tableAliasMap.isEmpty()) {
                                    tableName = tableAliasMap.values().iterator().next();
                                }
                            }
                        }

                        QueryFieldDTO fieldDTO = QueryFieldDTO.builder()
                            .name(fieldName)
                            .type(inferFieldType(fieldName))
                            .label(fieldLabel)
                            .tableName(tableName)
                            .build();
                        fields.add(fieldDTO);
                    }
                }
            } else {
                throw new RuntimeException("sql语法错误，未找到SELECT和FROM之间的内容");
            }
        } catch (Exception e) {
            log.error("解析SQL字段失败: ", e);
            throw new RuntimeException(e.getMessage());
        }

        return fields;
    }

    /**
     * 提取SQL中的表名和别名的映射关系
     */
    private Map<String, String> extractTableAliasMap(String sql) {
        Map<String, String> aliasMap = new HashMap<>();

        // 添加null检查
        if (sql == null || sql.trim().isEmpty()) {
            return aliasMap;
        }

        try {
            // 提取FROM后面的表部分，处理有别名和无别名的情况
            // 支持 database.table 格式的表名
            // 首先提取FROM子句到WHERE/GROUP BY/ORDER BY/LIMIT/JOIN等关键字之前的所有内容
            Pattern fromClausePattern = Pattern.compile(
                "\\bFROM\\s+([^\\s]+.*?)(?:\\s+WHERE|\\s+GROUP\\s+BY|\\s+ORDER\\s+BY|\\s+LIMIT|\\s+(?:LEFT|RIGHT|INNER)?\\s*JOIN|\\s+WITH|$)",
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL
            );
            Matcher fromClauseMatcher = fromClausePattern.matcher(sql);

            if (fromClauseMatcher.find()) {
                String fromClause = fromClauseMatcher.group(1).trim();
                // 按逗号分割多个表
                String[] tables = fromClause.split(",");

                for (String tableExpr : tables) {
                    tableExpr = tableExpr.trim();
                    // 对每个表表达式使用原来的正则表达式逻辑
                    Pattern tablePattern = Pattern.compile(
                        "^((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?$",
                        Pattern.CASE_INSENSITIVE
                    );
                    Matcher tableMatcher = tablePattern.matcher(tableExpr);

                    if (tableMatcher.find()) {
                        String fullTableName = tableMatcher.group(1);
                        // 从 database.table 格式中提取表名部分
                        String tableName = extractTableNameFromFullName(fullTableName);
                        // 如果没有别名，使用表名作为别名
                        String alias = tableMatcher.group(2) != null ? tableMatcher.group(2) : tableName;
                        aliasMap.put(alias, tableName);
                        log.info("Found table: {} (full: {}) with alias: {}", tableName, fullTableName, alias);
                    }
                }
            }

            // 处理JOIN，同样处理有别名和无别名的情况
            // 支持 database.table 格式的表名
            Pattern joinPattern = Pattern.compile(
                "\\b(?:LEFT|RIGHT|INNER)?\\s*JOIN\\s+((?:\\w+\\.)?\\w+)(?:\\s+(?:AS\\s+)?(\\w+))?(?:\\s+ON|\\s+WHERE|\\s+GROUP|\\s+ORDER|\\s+LIMIT|\\s+WITH|\\s*$)",
                Pattern.CASE_INSENSITIVE
            );
            Matcher joinMatcher = joinPattern.matcher(sql);

            while (joinMatcher.find()) {
                String fullTableName = joinMatcher.group(1);
                // 从 database.table 格式中提取表名部分
                String tableName = extractTableNameFromFullName(fullTableName);
                // 如果没有别名，使用表名作为别名
                String alias = joinMatcher.group(2) != null ? joinMatcher.group(2) : tableName;
                aliasMap.put(alias, tableName);
                log.info("Found joined table: {} (full: {}) with alias: {}", tableName, fullTableName, alias);
            }

            log.info("Final table alias mapping: {}", aliasMap);
        } catch (Exception e) {
            log.error("解析表别名失败: {}", e.getMessage(), e);
        }

        return aliasMap;
    }

    /**
     * 从完整表名中提取表名部分
     * 例如：database.table -> table，table -> table
     */
    private String extractTableNameFromFullName(String fullTableName) {
        if (fullTableName == null || fullTableName.trim().isEmpty()) {
            return fullTableName;
        }

        // 如果包含点号，取最后一部分作为表名
        if (fullTableName.contains(".")) {
            String[] parts = fullTableName.split("\\.");
            return parts[parts.length - 1];
        }

        return fullTableName;
    }

    /**
     * 提取SQL中的表名
     */
    private Set<String> extractTableNames(String sql) {
        Set<String> tableNames = new HashSet<>();

        try {
            // 1. 提取FROM子句，支持 database.table 格式
            Pattern fromPattern = Pattern.compile("FROM\\s+([\\w\\.\\s,]+)(?:\\s+WHERE|\\s+GROUP|\\s+ORDER|\\s+LIMIT|\\s+JOIN|$)", Pattern.CASE_INSENSITIVE);
            Matcher fromMatcher = fromPattern.matcher(sql);

            if (fromMatcher.find()) {
                String fromClause = fromMatcher.group(1).trim();
                String[] tables = fromClause.split(",");

                for (String table : tables) {
                    // 去除表的别名，取第一个词作为表名（可能包含database.table格式）
                    String fullTableName = table.trim().split("\\s+")[0];
                    // 从 database.table 格式中提取表名部分
                    String tableName = extractTableNameFromFullName(fullTableName);
                    tableNames.add(tableName);
                }
            }

            // 2. 提取JOIN子句中的表名，支持 database.table 格式
            Pattern joinPattern = Pattern.compile("JOIN\\s+((?:\\w+\\.)?\\w+)", Pattern.CASE_INSENSITIVE);
            Matcher joinMatcher = joinPattern.matcher(sql);

            while (joinMatcher.find()) {
                String fullTableName = joinMatcher.group(1);
                // 从 database.table 格式中提取表名部分
                String tableName = extractTableNameFromFullName(fullTableName);
                tableNames.add(tableName);
            }
        } catch (Exception e) {
            log.error("解析SQL表名失败: {}", e.getMessage(), e);
        }

        return tableNames;
    }

    /**
     * 将数据库列类型转换为字段类型
     */
    private String convertColumnTypeToFieldType(String columnType) {
        String type = columnType.toLowerCase();

        if (type.contains("decimal") ||
            type.contains("numeric") ||
            type.contains("float") ||
            type.contains("double")) {
            return "number";
        } else if (type.contains("date") ||
                   type.contains("time") ||
                   type.contains("timestamp")) {
            return "date";
        } else if (type.equals("boolean") ||
                   type.equals("bool") ||
                   type.equals("bit")) {
            return "boolean";
        }

        return "string";
    }

    /**
     * 推断字段类型
     */
    private String inferFieldType(String fieldName) {
        fieldName = fieldName.toLowerCase();

        if (fieldName.contains("id") ||
            fieldName.contains("count") ||
            fieldName.contains("num") ||
            fieldName.contains("age")) {
            return "number";
        } else if (fieldName.contains("date") ||
                   fieldName.contains("time") ||
                   fieldName.endsWith("_at") ||
                   fieldName.endsWith("_dt")) {
            return "date";
        } else if (fieldName.startsWith("is_") ||
                   fieldName.startsWith("has_") ||
                   fieldName.contains("flag") ||
                   fieldName.contains("status")) {
            return "boolean";
        }

        return "string";
    }

    /**
     * 驼峰命名转标题形式
     */
    private String camelCaseToTitleCase(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toUpperCase(camelCase.charAt(0)));

        for (int i = 1; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                result.append(' ');
            }
            result.append(c);
        }

        return result.toString();
    }

    /**
     * 检查字符串是否在字符串字面量内部
     */
    private boolean isInStringLiteral(String sql, int position) {
        // 匹配单引号或双引号包裹的字符串
        Pattern stringPattern = Pattern.compile("(['\"]).*?\\1");
        Matcher stringMatcher = stringPattern.matcher(sql);

        while (stringMatcher.find()) {
            int start = stringMatcher.start();
            int end = stringMatcher.end();
            // 检查参数位置是否在当前字符串字面量的范围内
            if (position >= start && position < end) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数位置记录类，用于跟踪参数在SQL中的位置
     */
    private static class ParameterPosition {
        int start;
        int end;
        boolean processed = false;

        ParameterPosition(int start, int end) {
            this.start = start;
            this.end = end;
        }
    }
}
