# QueryServiceImpl 多表联查字段覆盖问题修复

## 问题描述

在原有的 `QueryServiceImpl.executeSql()` 方法中，使用 `JdbcTemplate.queryForList(sql)` 执行多表联查时，如果多个表包含相同的字段名，会发生字段覆盖现象。

### 问题示例

假设有以下SQL查询：
```sql
SELECT u.id, u.name, d.id, d.name 
FROM user u 
JOIN department d ON u.dept_id = d.id
```

**修改前的问题：**
- `template.queryForList(sql)` 返回的 `Map<String, Object>` 中
- `u.id` 和 `d.id` 都会被映射为键 `"id"`，导致后者覆盖前者
- `u.name` 和 `d.name` 都会被映射为键 `"name"`，导致后者覆盖前者
- 最终结果只能获取到 `d.id` 和 `d.name`，`u.id` 和 `u.name` 丢失

## 解决方案

### 1. 使用自定义 RowMapper

创建了 `createNoDuplicateRowMapper()` 方法，通过以下策略避免字段覆盖：

1. **优先使用别名**：如果SQL中有别名（columnLabel），优先使用别名作为字段名
2. **表名前缀**：如果检测到字段名重复且有表名信息，使用 `表名.字段名` 格式
3. **数字后缀**：如果仍有重复，添加数字后缀如 `字段名_2`

### 2. 修改后的效果

**修改后的结果：**
```json
{
  "id": "user_id_value",      // u.id 的值
  "name": "user_name_value",  // u.name 的值  
  "department.id": "dept_id_value",      // d.id 的值
  "department.name": "dept_name_value"   // d.name 的值
}
```

或者如果没有表名信息：
```json
{
  "id": "user_id_value",      // u.id 的值
  "name": "user_name_value",  // u.name 的值
  "id_2": "dept_id_value",    // d.id 的值
  "name_2": "dept_name_value" // d.name 的值
}
```

## 代码修改详情

### 1. 添加导入
```java
import org.springframework.util.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
```

### 2. 添加私有方法
```java
private RowMapper<Map<String, Object>> createNoDuplicateRowMapper() {
    return new RowMapper<Map<String, Object>>() {
        @Override
        public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            Map<String, Object> row = new LinkedHashMap<>();
            
            // 记录已使用的列名，用于处理重复列名
            Map<String, Integer> columnNameCount = new HashMap<>();
            
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                String columnLabel = metaData.getColumnLabel(i);
                String tableName = metaData.getTableName(i);
                
                // 优先使用columnLabel，如果没有则使用columnName
                String finalColumnName = StringUtils.hasText(columnLabel) ? columnLabel : columnName;
                
                // 如果有表名信息且列名重复，使用 表名.列名 的格式
                if (StringUtils.hasText(tableName) && columnNameCount.containsKey(finalColumnName)) {
                    finalColumnName = tableName + "." + finalColumnName;
                }
                
                // 如果还是重复，添加数字后缀
                String uniqueColumnName = finalColumnName;
                int count = columnNameCount.getOrDefault(finalColumnName, 0);
                if (count > 0) {
                    uniqueColumnName = finalColumnName + "_" + (count + 1);
                }
                columnNameCount.put(finalColumnName, count + 1);
                
                Object value = rs.getObject(i);
                row.put(uniqueColumnName, value);
            }
            
            return row;
        }
    };
}
```

### 3. 替换查询调用
```java
// 修改前
List<Map<String, Object>> rows = template.queryForList(sql);

// 修改后  
List<Map<String, Object>> rows = template.query(sql, createNoDuplicateRowMapper());
```

## 最佳实践建议

### 1. SQL层面的解决方案
最好的做法还是在SQL层面使用别名来避免字段名冲突：
```sql
SELECT u.id as user_id, u.name as user_name, 
       d.id as dept_id, d.name as dept_name 
FROM user u 
JOIN department d ON u.dept_id = d.id
```

### 2. 前端适配
前端在处理查询结果时，需要适配新的字段名格式，特别是带有表名前缀或数字后缀的字段。

### 3. 文档更新
建议在API文档中说明，当多表联查出现字段名冲突时，系统会自动添加表名前缀或数字后缀来区分字段。

## 影响评估

### 1. 向后兼容性
- 对于没有字段名冲突的查询，返回结果保持不变
- 对于有字段名冲突的查询，现在能够返回完整数据（之前会丢失数据）

### 2. 性能影响
- 使用自定义RowMapper会有轻微的性能开销，但可以忽略不计
- 相比数据丢失的风险，这个开销是值得的

### 3. 测试建议
建议添加以下测试用例：
- 多表联查有重复字段名的情况
- SQL使用别名的情况  
- 单表查询的回归测试 