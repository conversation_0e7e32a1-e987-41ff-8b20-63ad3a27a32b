package com.datascope.main.config;

import com.datascope.app.security.EncryptionServiceFactory;
import com.yeepay.g3.utils.common.datasource.Config;
import com.yeepay.g3.utils.common.encrypt.AES;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

/**
 * Redis配置类
 * 使用Lettuce客户端连接Redis单机模式
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Configuration
public class RedisConfig {

    @Autowired
    private AppProperties appProperties;

    @Value("${sentinels}")
    private String sentinels;

    @Value("${masterName}")
    private String masterName;

    @Value("${maxTotal}")
    private int maxTotal;

    @Value("${maxIdle}")
    private int maxIdle;

    @Value("${maxWaitMillis}")
    private long maxWaitMillis;

    @Value("${testOnBorrow}")
    private boolean testOnBorrow;

    @Value("${testOnReturn}")
    private boolean testOnReturn;

    @Value("${timeout}")
    private long timeout;

    @Value("${newVersion}")
    private boolean newVersion;

    @Value("${mode}")
    private String mode;

    @Value("${password}")
    private String password;

    /**
     * 从sentinels配置中解析第一个地址作为单机连接
     */
    private String[] parseRedisAddress() {
        String[] sentinelAddresses = sentinels.split(",");
        if (sentinelAddresses.length > 0) {
            String firstAddress = sentinelAddresses[0].trim();
            String[] parts = firstAddress.split(":");
            if (parts.length == 2) {
                return new String[]{parts[0], parts[1]};
            }
        }
        // 默认值
        return new String[]{"localhost", "6379"};
    }

    /**
     * 解密Redis密码
     */
    private String decryptPassword(String encryptedPassword) {
        if (!StringUtils.hasText(encryptedPassword)) {
            return null;
        }

        try {
            // 使用项目内部的AES加密服务进行解密
            return AES.decryptFromBase64(password, Config.AES_KEY);
        } catch (Exception e) {
            log.warn("Redis密码解密失败，将使用原始密码: {}", e.getMessage());
            return encryptedPassword;
        }
    }

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        // 从sentinels配置中解析第一个地址作为单机连接
        String[] address = parseRedisAddress();
        String host = address[0];
        int port = Integer.parseInt(address[1]);

        // 配置Redis单机连接
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        config.setDatabase(appProperties.getRedis().getDatabase());

        // 设置密码（解密后）
        String decryptedPassword = decryptPassword(password);
        if (StringUtils.hasText(decryptedPassword)) {
            config.setPassword(decryptedPassword);
        }

        // 配置Lettuce连接池
        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
            .commandTimeout(Duration.ofMillis(timeout))
            .poolConfig(jedisPoolConfig())
            .build();

        return new LettuceConnectionFactory(config, clientConfig);
    }

    /**
     * 配置连接池参数
     */
    private org.apache.commons.pool2.impl.GenericObjectPoolConfig jedisPoolConfig() {
        org.apache.commons.pool2.impl.GenericObjectPoolConfig poolConfig =
            new org.apache.commons.pool2.impl.GenericObjectPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxWaitMillis(maxWaitMillis);
        poolConfig.setTestOnBorrow(testOnBorrow);
        poolConfig.setTestOnReturn(testOnReturn);
        return poolConfig;
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化器
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(connectionFactory);
        return template;
    }

    @Bean
    public RedisClient redisClient() {
        // 从sentinels配置中解析第一个地址作为单机连接
        String[] address = parseRedisAddress();
        String host = address[0];
        int port = Integer.parseInt(address[1]);

        RedisURI.Builder builder = RedisURI.builder()
            .withHost(host)
            .withPort(port)
            .withDatabase(appProperties.getRedis().getDatabase());

        // 设置密码（解密后）
        String decryptedPassword = decryptPassword(password);
        if (StringUtils.hasText(decryptedPassword)) {
            builder.withPassword(decryptedPassword.toCharArray());
        }

        builder.withTimeout(Duration.ofMillis(timeout));
        return RedisClient.create(builder.build());
    }

    @Bean
    public StatefulRedisConnection<String, String> redisConnection(RedisClient redisClient) {
        return redisClient.connect();
    }

    @Bean
    public RedisCommands<String, String> redisCommands(StatefulRedisConnection<String, String> connection) {
        return connection.sync();
    }
}
